import {
	http
} from '@/common/request/index.js'; // 局部引入


const api = {
	terminalPage: '/wechat/terminalrecord/terminalPage',
	terminalrecordPage: '/wechat/terminalrecord/page',
	terminalInfo: '/crm/terminal/info',
	updateCrmTerminal: '/wechat/terminalrecord/updateCrmTerminal',
	terminalrecordAdd: '/wechat/terminalrecord/add',
	terminalrecordUpdate: '/wechat/terminalrecord/update',
	terminalrecordInfo: '/wechat/terminalrecord/info',
}

export const terminalPage = (data) => {
	return http.post(api.terminalPage, data);
}

export const terminalrecordPage = (params) => {
	return http.get(api.terminalrecordPage, {params});
}

export const terminalInfo = (params) => {
	return http.get(api.terminalInfo, {params});
}

export const updateCrmTerminal = (data) => {
	return http.post(api.updateCrmTerminal, data);
}

export const terminalrecordAdd = (data) => {
	return http.post(api.terminalrecordAdd, data);
}

export const terminalrecordUpdate = (data) => {
	return http.post(api.terminalrecordUpdate, data);
}

export const terminalrecordInfo = (params) => {
	return http.get(api.terminalrecordInfo, {params});
}


