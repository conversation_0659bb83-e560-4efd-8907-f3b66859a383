<template>
  <view class="comment-page">
    <view class="page-content">
      <view class="visit-target">
        <view class="text-infos">
          <text class="name">{{ clientDoctor }}</text>
          <text class="address">{{ clientShop.address }}</text>
        </view>
      </view>
      <view class="comm-title">请对您的协访对象进行评价：</view>
      <view
        class="comm-target comm-panel"
        v-if="signoutParams.agentIsPresent == 1"
      >
        <text class="top-panel">
          代理商：
          <text class="stress-text">{{ agent.agentName }}</text>
        </text>
        <text class="top-panel">综合评价：</text>
        <view class="rate-panel">
          <uv-rate
            :active-color="defaultConfig.activeColor"
            :inactive-color="defaultConfig.inactiveColor"
            :gutter="defaultConfig.gutter"
            :size="defaultConfig.size"
            :min-count="0"
            :allowHalf="true"
            touchable
            v-model="targetValue"
          ></uv-rate>
        </view>
      </view>

      <view class="comm-panel comm-list">
        <view v-if="signoutParams.crmIsPresent == 1">
          <view class="comm-item" style="margin-bottom: 22rpx">
            <text class="top-panel">
              招商代表：
              <text class="stress-text">{{ clientShop.userName }}</text>
            </text>
          </view>

          <view
            class="comm-item"
            v-for="(item, index) in rateItems"
            :key="index"
          >
            <text class="top-panel">{{ item.label }}：</text>
            <view class="rate-panel">
              <uv-rate
                :active-color="defaultConfig.activeColor"
                :inactive-color="defaultConfig.inactiveColor"
                :gutter="defaultConfig.gutter"
                :size="defaultConfig.size"
                :min-count="0"
                :allowHalf="true"
                touchable
                v-model="item.value"
              ></uv-rate>
            </view>
          </view>
        </view>
        <view class="remark-panel">
          <uv-textarea
            adjustPosition
            v-model="remarkText"
            :textStyle="textareaConfig.textStyle"
            :placeholderStyle="textareaConfig.placeholderStyle"
            :customStyle="textareaConfig.customStyle"
            placeholder="请留下您的协访建议："
            border="none"
            maxlength="200"
          ></uv-textarea>
        </view>
      </view>

      <view class="add-comm" @click="submitFun">提交</view>
    </view>
    <p-tabbar tabbarSelect="work"></p-tabbar>
  </view>
</template>

<script setup>
import { ref, computed } from "vue";
import { useVisitStore } from "@/common/store/visit";
import { saveSignIn, saveCoachedEvaluation } from "@/common/api/sign";
import { onLoad } from "@dcloudio/uni-app";

const visitStore = useVisitStore();

const id = ref(""); //主键id

onLoad((option) => {
  id.value = option.id;
});

const defaultConfig = {
  inactiveColor: "#b2b2b2",
  gutter: 8,
  size: "48rpx",
  activeColor: "#F9BE3A",
};
const textareaConfig = {
  placeholderStyle: {
    color: "rgba(29, 29, 29, 0.60)",
    "font-family": "OPPOSans",
    "font-size": "28rpx",
    "font-style": "normal",
    "font-weight": "400",
    "line-height": "36.4rpx" /* 36.4rpx */,
  },
  textStyle: {
    color: "#1D1D1D",
    "font-family": "OPPOSans",
    "font-size": "28rpx",
    "font-style": "normal",
    "font-weight": "400",
    "line-height": "36.4rpx" /* 36.4rpx */,
  },
  customStyle: {
    borderRadius: "16rpx",
    background: "#F1F1F1",
    height: "202rpx",
    overflow: "auto",
    "border-radius": "16rpx",
  },
};
const targetValue = ref(5);

const visitPlanRateValue = ref(5); // 访前计划评分
const workAttitudeRateValue = ref(5); // 工作态度评分

const clientInteractionRateValue = ref(5); // 客情管理评分
const benefitStatementRateValue = ref(5); // 陈述利益评分
const overallRatingRateValue = ref(5); // 综合评价评分

const remarkText = ref("");

const rateItems = ref([
  { label: "访前计划", value: visitPlanRateValue },
  { label: "工作态度", value: workAttitudeRateValue },
  { label: "客情管理", value: clientInteractionRateValue },
  { label: "陈述利益", value: benefitStatementRateValue },
  { label: "综合评价", value: overallRatingRateValue },
]);
const signoutParams = computed(() => {
  return visitStore.$state.signoutParams;
});
const clientShop = computed(() => {
  return visitStore.$state.clientShop || {};
});
const agent = computed(() => {
  return visitStore.$state.agent || {};
});
const clientDoctor = computed(() => {
  return visitStore.$state.clientDoctor?.name
    ? `${visitStore.$state.clientShop?.name}/${visitStore.$state.clientDept.departmentName}/${visitStore.$state.clientDoctor?.name}`
    : null;
});

const submitFun = () => {
  let parmas = {
    id: id.value,
    cusRelationshipManagement: clientInteractionRateValue.value, //客情管理
    evaluate: overallRatingRateValue.value,
    statementInterests: benefitStatementRateValue.value,
    workAttitude: workAttitudeRateValue.value,
    preVisitPlan: visitPlanRateValue.value,
    suggestion: remarkText.value,
    etEvaluate: targetValue.value,
  };
  saveCoachedEvaluation(parmas).then((res) => {
    if (res.code == 0) {
      uni.showToast({
        title: "评价成功",
        icon: "none",
      });
      visitStore.setActiveTab(1);
      setTimeout(() => {
        uni.reLaunch({
          url: "/pages/subVisit/signRecord/signRecord",
        });
      }, 2000);
    } else {
      uni.showToast({
        title: "评价失败",
        icon: "none",
      });
    }
  });
};

const onChange = () => {};
</script>

<style scoped lang="scss">
@import "@/static/scss/global.scss";
.comment-page {
  .page-content {
    @include globalPageStyle();
    box-sizing: border-box;
    padding: 28rpx 20rpx;
    overflow-y: auto;
    .visit-target {
      @include cardBgCommonStyle();
      height: 140rpx;
      padding-right: 12rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .text-infos {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        box-sizing: border-box;
        padding: 22rpx 24rpx;
        .name {
          @include setBoldFont(28rpx, 44rpx, #1d1d1d);
          margin-right: 18rpx;
        }
        .address {
          @include setBoldFont(20rpx, 36rpx, #8d9094);
          @include ellipsisBasic(1);
        }
      }

      &::after {
        content: "";
        width: 128rpx;
        height: 128rpx;
        @include setBackgroundImage("../static/iocn_assistanceReview.png");
      }
    }
    .comm-title {
      @include setBoldFont(28rpx, 44rpx, #fff);
      padding-top: 18rpx;
      padding-left: 24rpx;
      margin-bottom: 16rpx;
    }
    .comm-panel {
      &.comm-list {
        margin-top: 24rpx;
      }
      @include cardBgCommonStyle();
      padding: 20rpx 24rpx 28rpx;
      .top-panel {
        @include setlightFont(28rpx, 44rpx, #2f3133);
        .stress-text {
          @include setBoldFont(28rpx, 44rpx, #2f3133);
        }
      }
      .rate-panel {
        display: flex;
        justify-content: center;
      }
      .comm-item {
      }

      .remark-panel {
        margin-top: 16rpx;
      }
    }
    .add-comm {
      margin-top: 28rpx;
      @include commonButtonStyle();
    }
  }
}
</style>
