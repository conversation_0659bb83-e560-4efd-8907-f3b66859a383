<template>
	<view class="meetingPeople-panel">
		<view class="page-content">
			<view class="top-panel">
				<view class="search-panel">
					<p-search placeholderText="请输入搜索内容" @searchSumbit="acceptSearch"></p-search>
				</view>
			</view>
			<view class="middle-panel">
				<p-selectBar :menuListText="['招商员工', '代理商']" :selectIndex="barSelectIndex" @triggerBarChange="acceptBarChange"></p-selectBar>
			</view>
			<view class="bottom-panel">
				<scroll-view class="list-panel" scroll-y="true" @scrolltolower="throttledLoadMore" lower-threshold="200">
					<view class="content-box">
						<view class="client-item" v-for="(item, index) in list" :key="item.id" @click="triggerCheckbox(item)">
              <view class="left-panel">
                <text class="name">{{ item.name }}</text>
                <text class="dept">{{ item.departName }}</text>
              </view>
              <view class="right-panel">
                <van-checkbox shape="square" :value="checkedList.has(item)"></van-checkbox>
              </view>
						</view>

					</view>
				</scroll-view>
			</view>
      <view class="order-btn" @click="handleConfirmClick">确认选择</view>
		</view>
		<p-tabbar tabbarSelect="work"></p-tabbar>
	</view>
</template>

<script setup>
import { ref,reactive } from 'vue';
import {
  onLoad
} from '@dcloudio/uni-app';
import {getBdmTeamUserInfoWechatVo} from "@/common/api/meeting";
const keyword = ref('');
const barSelectIndex = ref(0);

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
});
const list = ref([])

const loading = ref(false)

const acceptSearch = (inptKeyword) => {
	keyword.value = inptKeyword;
  refresh()
};
const acceptBarChange = (barIndex) => {
	barSelectIndex.value = barIndex;
  refresh()
};
const menuChange = (e) => {
	activeMenu.value = e.currentTarget.dataset.menuindex;
};

const refresh = () => {
  pagination.currentPage = 1
  list.value = []
  getShopList()
}

const getShopList = () => {
  loading.value = true
  getBdmTeamUserInfoWechatVo(
      {
        userName: keyword.value,
        userType: barSelectIndex.value + 1,
        limit: pagination.currentPage,
        size: pagination.pageSize,
      }
  ).then(res => {
    list.value = list.value.concat(res.data.list)
  }).finally(() => {
    loading.value = false
  })
}

const throttledLoadMore = () => {
  if(loading.value) return
  pagination.currentPage++
  getShopList()
}


const handleConfirmClick = () => {
  const pages = getCurrentPages();
  const prePage = pages[pages.length - 2];

  prePage.$vm.selectMeetingPeople(Array.from(checkedList.value));
  uni.navigateBack();
}

const checkedList = ref(new Set())
const triggerCheckbox = (item) => {
  checkedList.value.has(item) ? checkedList.value.delete(item) : checkedList.value.add(item)
}

onLoad(() => {
  getShopList()
})
</script>

<style scoped lang="scss">
@import '@/static/scss/global.scss';
.meetingPeople-panel {
	.page-content {
		@include globalPageStyle();
		display: flex;
		flex-direction: column;
		.top-panel {
			padding: 26rpx 24rpx 18rpx;
			box-sizing: border-box;
			background: rgba(255, 255, 255, 0.9);
			/* 下层投影 */
			box-shadow: 0rpx 4rpx 16rpx 0rpx rgba(0, 0, 0, 0.12);
			display: flex;
			flex-direction: column;
			.menu-panel {
				display: flex;
				justify-content: flex-start;
				margin-bottom: 14rpx;
				@include setlightFont(28rpx, 36rpx, rgba(29, 29, 29, 0.6));
				.item {
					padding-bottom: 6rpx;
					margin-right: 32rpx;
					position: relative;
					&.active {
						@include setBoldFont(28rpx, 36rpx, #303030);
						&::before {
							content: '';
							height: 4rpx;
							background: #4068f5;
							width: 100%;
							position: absolute;
							border-radius: 4rpx;
							left: 0;
							bottom: -4rpx;
							right: 0;
						}
					}
				}
			}
			.search-panel {
				::v-deep .search-panel {
					// display: none;
					background: transparent;
					padding: 0;
					box-shadow: none;
				}
			}
		}
		.middle-panel {
			margin: 28rpx auto 0;
			width: 536rpx;
			height: 68rpx;
			@include cardBgCommonStyle();
			border-radius: 48rpx;

			.item {
				@include setlightFont(28rpx, 44rpx, #8d9094);
			}
		}
		.bottom-panel {
			flex: 1;
			overflow-y: hidden;
			height: 0;
			.list-panel {
				box-sizing: border-box;
				padding: 24rpx 20rpx;
				padding-right: 0;
				height: 100%;
				.content-box {
					box-sizing: border-box;
					padding-right: 20rpx;
					height: 100%;
				}

				.client-item {
          @include cardBgCommonStyle();
          margin-bottom: 24rpx;
          display: flex;
          justify-content: space-between;
          align-items: center;
          box-sizing: border-box;
          padding: 20rpx 24rpx;
					.title {
						@include setBoldFont(28rpx, 44rpx, #1d1d1d);
					}
					.address {
						margin-top: 12rpx;
						@include setlightFont(24rpx, 40rpx, rgba(29, 29, 29, 0.6));
					}
          .left-panel {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            .name {
              @include setBoldFont(28rpx, 44rpx, #303030);
            }
            .dept {
              @include setlightFont(28rpx, 44rpx, rgba(29, 29, 29, 0.6));
            }
          }
				}
			}
		}
	}
  .order-btn {
    width: 712rpx;
    margin: 0 auto;
    height: 64rpx;
    line-height: 64rpx;
    border-radius: 8rpx;
    background: linear-gradient(180deg, #52acff -12%, #263af0 117%);
    @include setlightFont(24rpx, 64rpx, #fff);
    text-align: center;
  }
}
</style>
