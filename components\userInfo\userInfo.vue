<template>
	<view class="userinfo-panel">
		<image class="userhead" src="/static/image/icon/comm/icon_userHead.png" mode=""></image>
		<view class="user-info">
			<view class="name-panel">
				<text class="name">{{ userDetail.name }}</text>
				<text class="user-tag" v-if="userDetail.posts"
					:class="colorList[userDetail.posts?.[0].code]">{{ userDetail.posts?.[0].name }}</text>
			</view>
			<view class="dept">{{ UserInfo.businessUnitIdName }}</view>
		</view>
	</view>
</template>

<script setup>
	import {
		USER_DETAIL_KEY,
		USER_INFO_KEY
	} from '/common/const/cache.js';
	const userDetail = uni.getStorageSync(USER_DETAIL_KEY);
	const UserInfo = uni.getStorageSync(USER_INFO_KEY);
	import {
		defineProps
	} from 'vue';
	const props = defineProps({
		type: {
			type: String
		}
	});
	console.log(userDetail, '-----getStorageSync');
	const colorList = {
		10005: 'user',
		10004: 'ka',
		10003: 'manager',
		10002: 'admin',
		10001: 'director'
	};
</script>

<style scoped lang="scss">
	@import '@/static/scss/global.scss';

	.userinfo-panel {
		display: flex;
		position: relative !important;
		justify-content: flex-start;

		.userhead {
			width: 140rpx;
			height: 140rpx;
			min-width: 140rpx;
			height: 140rpx;
		}

		.icon-right {
			width: 196.27rpx;
			height: 194.952rpx;
			position: absolute;
			opacity: 0.6;
			right: -20rpx;
			bottom: 18rpx;
		}

		.user-info {
			position: relative;
			z-index: 2;
			margin-left: 14rpx;

			.name-panel {
				display: flex;
				align-items: center;
				margin-top: 10rpx;

				.name {
					@include setBoldFont(40rpx, 52rpx, #fff);
				}

				.user-tag {
					@include setBoldFont(20rpx, 38rpx, #fff);
					border-radius: 8rpx;
					height: 38rpx;
					min-width: 50rpx;
					margin-left: 20rpx;
					text-align: center;
					padding: 3rpx 8rpx;

					&.user {
						background: #21c369;
					}

					&.ka {
						background: #ba4ecc;
					}

					&.manager {
						background: #c32e21;
					}

					&.director {
						background: #21c3c0;
					}

					&.admin {
						background: #2145c3;
					}
				}

				.gender {
					margin-left: 16rpx;
					width: 32rpx;
					height: 32rpx;
				}
			}

			.dept {
				@include setBoldFont();
				color: #fff;
				width: 80%;
				margin-top: 28rpx;
			}
		}
	}
</style>
