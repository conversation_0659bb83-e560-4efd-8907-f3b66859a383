import {
	http
} from '@/common/request/index.js'; // 局部引入


const api = {
	getCrmTerminalWechatVo: '/wechat/sign/getCrmTerminalWechatVo',
	getCoachedCrmTerminalWechatVo: '/wechat/sign/getCoachedCrmTerminalWechatVo',
	deptList: '/wechat/sign/deptList',
	dockerList: '/wechat/sign/dockerList',
	getByCurrentTime: '/wechat/sign/GetByCurrentTime',
	checkOtcSignIn: '/wechat/sign/checkOtcSignIn',
	saveSignIn: '/wechat/sign/saveSignIn',
	getOtcSignIn: '/wechat/sign/info',
	listByDate: '/wechat/sign/listByDate',
	coachedUserList: '/organization/user/coachedUserList',
	saveCoachedEvaluation: '/wechat/sign/saveCoachedEvaluation',
	coachedpage: '/wechat/sign/coachedpage',
	saveOtcAccountAddress: '/wechat/sign/saveOtcAccountAddress',
	deptAdd: '/wechat/sign/deptAdd',
	addDoctor: '/wechat/sign/addDoctor',
	getPunchSetById: '/wechat/sign/getPunchSetById',
	terminalproduct: '/crm/terminalproduct/page',
	signInfo: '/wechat/sign/info',
	checkOtcAccountAddress: '/wechat/sign/checkOtcAccountAddress',
	deptTree: '/crm/terminal/dept-tree'
}

//查询当前时间该用户的拜访记录(返回最近的未签退的记录)
export const getByCurrentTime = (params) => {
	return http.post(api.getByCurrentTime, params)

}
// 校验打卡是否超范围
export const checkOtcSignIn = (params) => {
	return http.get(api.checkOtcSignIn +
		`?purMerchantLatitude=${params.purMerchantLatitude}&purMerchantLongitude=${params.purMerchantLongitude}&latitude=${params.latitude}&longitude=${params.longitude}`
	)
}

// 根据id查询OtcSignIn信息
export const getOtcSignIn = (params) => {
	return http.get(api.getOtcSignIn +
		`?id=${params}`
	)
}
// 签到
export const saveSignIn = (params) => {
	return http.post(api.saveSignIn, params)
}


// 签到记录
export const listByDate = (params) => {
	return http.get(api.listByDate +
		`?createDate=${params.createDate}&personId=${params.personId}&visitType=${params.visitType}`
	)
}

// 协访人员查询（不分页）
export const coachedUserList = (params) => {
	return http.get(api.coachedUserList +
		`?postId=${params.postId}&departmentId=${params.departmentId}${params.keyword ? '&keyword=' + params.keyword : ''}`
	)
}

// 协访评价
export const saveCoachedEvaluation = (params) => {
	return http.post(api.saveCoachedEvaluation, params)
}

// 下属协访列表
export const coachedpage = (params) => {
	return http.post(api.coachedpage, params)
}

// 上报更新地址
export const saveOtcAccountAddress = (data) => {
	return http.post(api.saveOtcAccountAddress, data)
}

export const crmTerminalWechatVo = (params) => {
	return http.post(api.getCrmTerminalWechatVo, params);
}
export const getCoachedCrmTerminalWechatVo = (params) => {
	return http.post(api.getCoachedCrmTerminalWechatVo, params);
}

export const deptList = (params) => {
	return http.get(api.deptList, { params });
}

export const dockerList = (params) => {
	return http.get(api.dockerList, { params });
}


export const deptAdd = (data) => {
	return http.post(api.deptAdd, data);
}

export const addDoctor = (data) => {
	return http.post(api.addDoctor, data);
}

export const getPunchSetById = (params) => {
	return http.get(api.getPunchSetById, { params });
}

export const terminalproduct = (params) => {
	return http.get(api.terminalproduct, { params });
}

export const signInfo = (params) => {
	return http.get(api.signInfo, { params });
}

export const checkOtcAccountAddress = (data) => {
	return http.post(api.checkOtcAccountAddress, data);
}

export const deptTree = (params) => {
	return http.get(api.deptTree, { params });
}
