<template>
  <view class="meetingDetails-panel">
    <view class="page-content">
      <view class="top-panel">
        <view class="meeting-detail">
          <view class="meet-status">
            <text class="text-info UPCOMING">{{
              MEETING_STATUS[meetingInfo.status]
            }}</text>
            <view class="more-btn" v-if="meetingInfo.status == 3">
              <view class="more-button" v-show="isVisibleMoreButton">
                <view class="cancel-meeting" @click="meetingCancelClick"
                  >取消会议</view
                >
              </view>
            </view>
          </view>
          <view class="meet-lable">{{ meetingInfo.actCategoryname }}</view>
          <view class="meet-name">{{ meetingInfo.meetTheme }}</view>
          <view class="meet-info">
            <text style="margin-right: 100rpx">
              负责人：
              <text class="stress-text">{{
                meetingInfo.personChargeName
              }}</text>
            </text>
            <text>
              SPU：
              <text class="stress-text">{{ meetingInfo.productName }}</text>
            </text>
          </view>

          <view class="meet-info">
            <text style="margin-right: 70rpx">
              参会终端：
              <text class="stress-text">{{ meetingInfo.terminalName }}</text>
            </text>
            <text>
              参会人数：
              <text class="stress-text">{{ meetingInfo.peopleNum }}</text>
            </text>
          </view>
          <view class="meet-time">
            召开时间：
            <text class="stress-text">{{ meetingInfo.meetingTime }}</text>
          </view>
          <view class="meeting-des" @click="toDetail">会议详情</view>
        </view>

        <!-- 审批流程 -->
        <view
          v-if="MeetingStatus == '4' || MeetingStatus == '5'"
          class="meeting-detail-panel"
        >
          <view
            class="meeting-info"
            v-for="(group, groupKey) in allMeetingData"
            :key="groupKey"
          >
            <van-cell
              v-for="(item, key) in group"
              :key="key"
              :is-link="item.isLink && !isInputField(key)"
              :title="item.title"
            >
              <!-- 如果是输入框项（如 会议主题、预算费用、参会人数），显示输入框 -->
              <input
                v-if="isInputField(key) && !item.readonly"
                v-model="item.value"
                placeholder="请输入"
                class="meeting-input"
                maxlength="30"
              />
              <!-- 否则显示文本 -->
              <text v-else>{{ item.label }}</text>
            </van-cell>
          </view>
          <!-- 上传图片？是否要保留 -->
          <view class="add-panel" v-if="meetingInfo.file">
            <view class="img-item">
              <view class="left-content">
                <image
                  class="thumbnail"
                  src="/pages/subVisit/static/iocn_assistanceReview.png"
                  mode=""
                ></image>
                <view>
                  <view
                    v-for="file in meetingInfo.file"
                    @click="download(file.fileUrl, file)"
                    class="text"
                    >{{ file.fileName }}.{{ file.fileSuffiex }}</view
                  >
                </view>
              </view>
            </view>
            <!--						<view class="add-btn">-->
            <!--							<view class="icon-panel">-->
            <!--								<image class="icon" src="../static/icon_add.png" mode=""></image>-->
            <!--							</view>-->
            <!--							<text>添加会议附件</text>-->
            <!--						</view>-->
          </view>
          <view class="remark-panel">
            <uv-textarea
              v-model="meetingSummarize"
              disabled
              count
              maxlength="500"
              adjustPosition
              border="none"
            ></uv-textarea>
          </view>
        </view>

        <view class="steps-panel" v-if="approvalList.length">
          <view class="title">审批流程</view>
          <view class="content">
            <view
              class="step-item"
              v-for="(item, index) in approvalList"
              :key="index"
            >
              <view class="index-num">{{ index + 1 }}</view>
              <view class="item-detail">
                <view class="name">
                  <text class="stress-text">{{ item.activityName }}</text>
                  <text>{{ item.nodes[0].approvaler[0].name }}</text>
                </view>
                <view class="status">
                  <!--									<text>审批意见</text>-->
                  <text class="stress-text">{{
                    APPROVAL_STATUS[item.nodes[0].displayStatus]
                  }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view
        class="bottom-panel"
        v-if="shouldShowBottomPanel"
        @click="meetingButtonClick"
      >
        <text>{{ MeetingButtonText }}</text>
      </view>
      <view class="bottom-panel" v-if="meetingInfo.status == 4" @click="finish">
        <text>结束会议</text>
      </view>
    </view>
    <p-tabbar tabbarSelect="work"></p-tabbar>
  </view>
</template>

<script setup>
// 初始化获取当前这个会议的状态
// 然后将对应的值注入到meetingAttend和meetingBudget，
// 进行中和已召开通过readonly区分，如果是进行中readonly赋值false 、 已召开赋值true
// 审批驳回的会议，点击修改，跳转到会议详情页，可以服用
import { computed, ref } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import {
  academicconferencesinfo,
  bdmteamconferencesinfoInfo,
  getApproveUrl,
  getBdmConferenceInformationInfo,
  getBdmConferenceSummarizeInfo,
  getFile,
  updateStatus,
} from "@/common/api/meeting";

const MEETING_STATUS = {
  0: "草稿",
  1: "审批中",
  2: "审批驳回",
  3: "待执行",
  4: "进行中",
  5: "已召开",
  6: "已取消",
};
const APPROVAL_STATUS = {
  NOTSTARTED: "未启动",
  INPROGRESS: "进行中",
  FINISHED: "已完成",
  CENCELLED: "被取消",
  DRAFT: "草稿",
  REVOKE: "被撤回",
  TIMEOUT_CANCELED: "超市作废",
  PENDING: "待处理",
  AGREED: "已同意",
  DISAGREED: "已拒绝",
  TRANSFERRED: "已转办",
  SUBMITTED: "已提交",
  ABORTED: "已终止",
  CANCELED: "已取消",
  VOIDED: "已作废",
};
const MeetingStatus = ref("3");
const meetingSummarize = ref("");
const isVisibleMoreButton = ref(true);
const id = ref(null);
const meetingType = ref(null);
// 审批流程步骤
const steps = [
  { text: "地区主管", desc: "赵子龙" },
  { text: "省区主管", desc: "艾德" },
];
const meetingAttend = ref({
  meetingTerminal: {
    title: "实际参会终端",
    value: "",
    label: "",
    isLink: false,
    readonly: true,
  },
  meetingPeople: {
    title: "实际参会人数",
    value: "",
    label: "",
    isLink: false,
    readonly: true,
  },
});

const meetingBudget = ref({
  meetingCost: {
    title: "实际会议费用",
    value: "",
    label: "",
    isLink: false,
    readonly: true,
  },
});

const meetingInfo = ref({});

const approvalList = ref([]);

// 计算所有会议相关数据（方便 `v-for` 渲染）
const allMeetingData = computed(() => ({
  meetingAttend: meetingAttend.value,
  meetingBudget: meetingBudget.value,
}));

const download = (url, file) => {
  console.log(file, 11111);
  uni.downloadFile({
    url: url,
    success: function (res) {
      const filePath = res.tempFilePath;
      uni.openDocument({
        // type: file.fileSuffiex,
        fileType: file.fileSuffiex,
        filePath: encodeURI(filePath),
        showMenu: true,
        success: function (res) {
          console.log("打开文档成功");
        },
      });
    },
    fail: function (err) {
      console.log(err);
      uni.showToast({
        title: "下载失败",
        icon: "error",
      });
    },
  });
};
/**
 * @description 计算底部按钮文本
 */
const MeetingButtonText = computed(() => {
  switch (MeetingStatus.value) {
    case "0":
    case "2":
      return "去修改";
    case "3":
      return "开始会议";
    case "4":
      return "提交";
    default:
      return "";
  }
});

/**
 * @description 判断是否显示底部按钮
 */
const shouldShowBottomPanel = computed(() =>
  ["0", "3"].includes(MeetingStatus.value)
);

/**
 * @description 判断是否是输入框字段（避免误触弹出 `showActionSheet`）
 * @param {string} key - 字段名
 * @returns {boolean}
 */
const isInputField = (key) => ["meetingPeople", "meetingCost"].includes(key);

// 更多按钮显示隐藏
const toggleMoreButton = () => {
  isVisibleMoreButton.value = !isVisibleMoreButton.value;
};

const updateMeetingStatus = (status) => {
  updateStatus({
    id: id.value,
    status,
  }).then((res) => {
    if (res.code === 0) {
      const pages = getCurrentPages();
      const prePage = pages[pages.length - 2];
      prePage.$vm.refresh();
      uni.navigateBack();
    } else {
      uni.showToast({
        title: "失败",
        icon: "none",
      });
    }
  });
};

const meetingCancelClick = () => {
  uni.showModal({
    title: "提示",
    content: "确定取消会议吗？",
    success: (res) => {
      if (res.confirm) {
        updateMeetingStatus(6);
      } else if (res.cancel) {
        console.log("用户点击取消");
      }
    },
  });
};

const finish = () => {
  uni.showModal({
    title: "提示",
    content: "确定结束会议吗？",
    success: (res) => {
      if (res.confirm) {
        updateMeetingStatus(5);
      } else if (res.cancel) {
        console.log("用户点击取消");
      }
    },
  });
};

const meetingButtonClick = () => {
  switch (MeetingStatus.value) {
    case "0":
    case "2":
      uni.redirectTo({
        url: `/pages/subMeeting/meetingBooking/meetingBooking?meetingType=academic&id=${id.value}`,
      });
      break;
    case "3":
      updateMeetingStatus(4);
      break;
  }
};

const toDetail = () => {
  uni.redirectTo({
    url: `/pages/subMeeting/meetingBooking/meetingBooking?meetingType=academic&id=${id.value}`,
  });
};

const selectTerminalClick = () => {
  uni.navigateTo({
    url: `/pages/subMeeting/selectTerminal/selectTerminal`,
  });
};

const selectTerminal = (selection) => {
  if (!selection || selection.length === 0) {
    return;
  }
  meetingAttendees.value.meetingTerminal = {
    ...meetingAttendees.value.meetingTerminal,
    label: selection.map((item) => item.name).join("\n"),
    value: selection.map((item) => item.id).join(","),
  };
};

defineExpose({
  selectTerminal,
});

onLoad((option) => {
  id.value = option.id;
  meetingType.value = option.meetingType;
  academicconferencesinfo({ id: option.id }).then((res) => {
    meetingInfo.value = res.data;
    MeetingStatus.value = res.data.status;
    if (res.data.traceId) {
      getApproveUrl({ workflowInstanceId: res.data.traceId }).then((res) => {
        approvalList.value = res.data.data;
      });
    }

    if (meetingInfo.value.status == 4 || meetingInfo.value.status == 5) {
      getBdmConferenceInformationInfo({ id: option.id }).then((res) => {
        meetingAttend.value.meetingTerminal.label = res.data.terminalName;
        meetingAttend.value.meetingTerminal.value = res.data.terminalName;
        meetingAttend.value.meetingPeople.label = res.data.peopleNum;
        meetingAttend.value.meetingPeople.value = res.data.peopleNum;
      });
      getBdmConferenceSummarizeInfo({ id: option.id }).then((res) => {
        if (res.data.fileIds) {
          getFile({ folderId: res.data.fileIds }).then((fileRes) => {
            if (fileRes.data && fileRes.data.length > 0) {
              // meetingInfo.value.file = fileRes.data.map(item => item.fileName).join('\n')
              meetingInfo.value.file = fileRes.data;
            }
          });
        }
        meetingBudget.value.meetingCost.value = res.data.acRealCost;
        meetingBudget.value.meetingCost.label = res.data.acRealCost;
        meetingSummarize.value = res.data.meetingSummary;
      });
    }
  });
});
</script>

<style scoped lang="scss">
@import "@/static/scss/global.scss";
.meetingDetails-panel {
  .page-content {
    @include globalPageStyle();
    box-sizing: border-box;
    padding: 28rpx 20rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    overflow-y: auto;
    .meeting-detail {
      @include cardBgCommonStyle();
      padding: 20rpx 28rpx;
      @include cardBgCommonStyle();
      margin-bottom: 24rpx;
      position: relative;
      .meet-status {
        position: absolute;
        right: 20rpx;
        top: 20rpx;
        @include setBoldFont(28rpx, 40rpx, #fff);

        display: flex;
        justify-content: flex-start;
        align-items: center;
        .text-info {
          height: 40rpx;
          border-radius: 54rpx;
          padding: 0rpx 12rpx;
          &.PENDING_APPROVAL {
            background: linear-gradient(99deg, #9cb8ff 11.03%, #2d4be9 88.97%);
          }
          &.REJECTED {
            background: linear-gradient(99deg, #ff9c9c 11.03%, #e92d2d 88.97%);
          }
          &.UPCOMING {
            background: linear-gradient(99deg, #ffdd9c 11.03%, #e9bf2d 88.97%);
          }
          &.IN_PROGRESS {
            background: linear-gradient(99deg, #9eff9c 11.03%, #2de94b 88.97%);
          }
          &.COMPLETED,
          &.CANCEL {
            background: #b4b9bf;
          }
        }

        .more-btn {
          margin-left: 12rpx;
          position: relative;
          .icon {
            width: 48rpx;
            height: 48rpx;
          }
          .more-button {
            position: absolute;
            right: 0;
            top: 16px;
            .cancel-meeting {
              width: 120rpx;
              padding: 8rpx;
              border-radius: 8rpx;
              border: 2rpx solid #fc474c;
              padding: 8rpx;
              @include setBoldFont(24rpx, 40rpx, #fc474c);
              text-align: center;
            }
          }
        }
      }
      .meet-lable {
        @include setBoldFont(24rpx, 40rpx, #4068f5);
      }
      .meet-name {
        margin-bottom: 8rpx;
        @include setBoldFont(28rpx, 44rpx, #2f3133);
      }
      .meet-info,
      .meet-time {
        margin-top: 8rpx;
        @include setlightFont(24rpx, 40rpx, #8d9094);
        .stress-text {
          @include setBoldFont(24rpx, 40rpx, #2f3133);
        }
      }
      .meet-info:first-child {
        margin-right: 50rpx;
      }
      .meeting-des {
        @include setlightFont(24rpx, 40rpx, #4068f5);
        text-decoration: underline;
        text-align: center;
        margin-top: 8rpx;
      }
    }
    .meeting-detail-panel {
      .meeting-info {
        @include cardBgCommonStyle();
        box-sizing: border-box;
        padding: 0 8rpx;
        margin-bottom: 24rpx;

        :deep(.van-cell) {
          --cell-vertical-padding: 20rpx;
          --cell-horizontal-padding: 28rpx;
          --cell-background-color: transparent;
          border-bottom: 2rpx solid #ced4db;

          .van-cell__title {
            @include setBoldFont(28rpx, 44rpx, #2f3133);
            &::before {
              content: "*";
              @include setBoldFont(28rpx, 44rpx, #f00);
            }
          }
          .van-cell__value {
            @include setlightFont(28rpx, 44rpx, #8d9094);
          }
          .meeting-theme-input {
            @include setlightFont(28rpx, 44rpx, #8d9094);
          }
        }
        /* 让最后一个 van-cell 去掉 border */
        :deep(.van-cell:last-child) {
          border-bottom: none;
        }
      }
      .add-panel {
        @include cardBgCommonStyle();
        box-sizing: border-box;
        padding: 0 8rpx;
        .add-btn,
        .img-item {
          padding: 20rpx 24rpx;
          @include setBoldFont(28rpx, 44rpx, #2f3133);
          display: flex;
          justify-content: flex-start;
          align-items: center;
        }
        .img-item {
          border-bottom: 2rpx solid #ced4db;
          justify-content: space-between;
          .left-content {
            display: flex;
            justify-content: flex-start;
            align-items: center;
          }
          .thumbnail {
            width: 48rpx;
            height: 48rpx;
            margin-right: 12rpx;
          }
          .text {
            @include setlightFont(28rpx, 40rpx, #2f3133);
          }
          .detele {
            width: 32rpx;
            height: 32rpx;
          }
        }

        .add-btn {
          .icon-panel {
            width: 48rpx;
            height: 48rpx;
            margin-right: 12rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            .icon {
              width: 24rpx;
              height: 24rpx;
            }
          }
        }
      }
      .remark-panel {
        margin: 24rpx 0;
        @include cardBgCommonStyle();
        height: 254rpx;
        :deep(uv-textarea) {
          background-color: transparent !important;
          height: 100%;
          width: 100%;
          box-sizing: border-box;
          padding: 24rpx 28rpx;
          .uv-textarea__field {
            height: 100% !important;
          }
          .textarea-placeholder {
            @include setlightFont(28rpx, 32rpx, rgba(29, 29, 29, 0.6));
          }
          .uv-textarea__count {
            background-color: transparent !important;
          }
        }
      }
    }
    .steps-panel {
      @include cardBgCommonStyle();
      padding: 20rpx 28rpx;
      .title {
        @include setBoldFont(28rpx, 44rpx, #2f3133);
        margin-bottom: 22rpx;
      }
      .content {
        .step-item {
          display: flex;
          height: 86rpx;
          position: relative;
          margin-bottom: 70rpx;
          // 只有从第二个元素开始才有 `::after`
          &:not(:first-child)::before {
            content: "";
            width: 4rpx;
            height: 138rpx;
            top: -130rpx;
            background: #ced4db;
            /* 下层投影 */
            box-shadow: 0rpx 4rpx 16rpx 0rpx rgba(0, 0, 0, 0.12);
            position: absolute;
            left: 22rpx;
          }
          &:last-child {
            margin-bottom: 0rpx;
          }

          .index-num {
            z-index: 1;
            text-align: center;
            width: 40rpx;
            height: 40rpx;
            border: 2rpx solid #fff;
            border-radius: 50%;
            //background: #d9d9d9;
            background-color: #007aff;
            @include setlightFont(21rpx, 40rpx, #fff);
            margin-right: 22rpx;
            &.active {
              background: #38b865;
            }
          }
          .item-detail {
            display: flex;
            flex-direction: column;
            .name,
            .status {
              @include setlightFont(24rpx, 40rpx, #8d9094);
              .stress-text {
                @include setBoldFont(24rpx, 40rpx, #2f3133);
                &.REJECTED {
                  color: #fc474c;
                }
              }
              :first-child {
                margin-right: 18rpx;
              }
            }
            .status {
              margin-top: 6rpx;
            }
          }
        }
      }
    }
    .bottom-panel {
      margin-top: 24rpx;
      @include commonSquareButtonStyle();
    }
  }
}
</style>
