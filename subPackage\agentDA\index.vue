<template>
  <view class="agent-page">
    <!-- 顶部切换和搜索 -->
    <view class="header-section">
      <view class="tab-search-row">
        <!-- 储备/正式切换 -->
        <view class="tab-container">
          <view
            class="tab-item"
            :class="{ active: activeTab === 'reserve' }"
            @click="switchTab('reserve')"
          >
            储备
          </view>
          <view
            class="tab-item"
            :class="{ active: activeTab === 'formal' }"
            @click="switchTab('formal')"
          >
            正式
          </view>
        </view>

        <!-- 搜索框@input="onSearch" -->
        <view class="search-container">
          <van-field
            v-model="searchKeyword"
            placeholder="搜索代理商"
            border="{{ false }}"
            input-align="left"
            maxlength="200"
            @change="(event) => (searchKeyword = event.detail)"
          >
            <van-icon slot="left-icon" name="search" />
          </van-field>
          <view @click="onSearch" class="search-text">搜索</view>
        </view>
      </view>

      <!-- 省市选择 -->
      <view class="picker-row">
        <view class="picker-item" @click="openProvincePicker">
          <text>{{ selectedProvince || "全部省份" }}</text>
          <van-icon name="arrow-down" />
        </view>
        <view class="picker-item" @click="openCityPicker">
          <text>{{ selectedCity || "全部城市" }}</text>
          <van-icon name="arrow-down" />
        </view>
      </view>
    </view>

    <!-- 列表内容 -->
    <div class="list-wrapper">
      <scroll-view
        class="list-container"
        scroll-y="true"
        @scrolltolower="loadMore"
        lower-threshold="200"
      >
        <view class="agent-list">
          <view
            v-for="(item, index) in agentList"
            :key="item.id"
            class="agent-item"
          >
            <!-- 左滑操作区域 -->
            <van-swipe-cell :right-width="activeTab === 'reserve' ? 70 : 0">
              <view class="agent-content" @click="goToDetail(item)">
                <view class="agent-info">
                  <view class="company-name">{{ item.name }}</view>
                  <view class="location"
                    >{{ item.provinceName }}<text v-if="item.cityName">/</text
                    >{{ item.cityName }}</view
                  >
                  <view class="contact">
                    <text>联系电话：</text>
                    <text>{{ item.phone }}</text>
                  </view>
                </view>
                <view class="detail-btn" @click.stop="goToDetail(item)">
                  查看详情
                </view>
              </view>

              <!-- 右滑操作按钮 -->
              <view
                v-if="activeTab === 'reserve'"
                slot="right"
                class="swipe-actions"
              >
                <view class="action-btn edit-btn" @click="editAgent(item)">
                  <van-icon name="edit" />
                  <text>编辑</text>
                </view>
                <view class="action-btn delete-btn" @click="deleteAgent(item)">
                  <van-icon name="delete" />
                  <text>删除</text>
                </view>
              </view>
            </van-swipe-cell>
          </view>
        </view>

        <!-- 加载更多 -->
        <view v-if="loading" class="loading-more">
          <van-loading size="16px">加载中...</van-loading>
        </view>

        <!-- 没有更多数据 -->
        <view v-if="!hasMore && agentList.length > 0" class="no-more"></view>

        <!-- 空状态 -->
        <view v-if="agentList.length === 0 && !loading" class="task-panel">
          <image
            class="empty"
            src="/static/image/bgs/empty.png"
            mode=""
          ></image>
          <view class="empty-text">暂无数据</view>
        </view>
      </scroll-view>
    </div>

    <!-- 底部悬浮按钮 -->
    <view class="float-btn" @click="addAgent">
      <van-icon name="plus" />
      <text>新增储备代理商代表</text>
    </view>

    <!-- 省份选择器 -->
    <van-popup
      :show="showProvincePicker"
      position="bottom"
      custom-style="height: 40%"
      @close="showProvincePicker = false"
    >
      <van-picker
        :columns="provinceColumns"
        @confirm="onProvinceConfirm"
        @cancel="onProvinceCancel"
        show-toolbar
        title="选择省份"
      />
    </van-popup>

    <!-- 城市选择器 -->
    <van-popup
      :show="showCityPicker"
      position="bottom"
      custom-style="height: 40%"
      @close="showCityPicker = false"
    >
      <van-picker
        :columns="cityColumns"
        @confirm="onCityConfirm"
        @cancel="onCityCancel"
        show-toolbar
        title="选择城市"
      />
    </van-popup>
  </view>
</template>

<script setup>
import { ref } from "vue";
import { onShow } from "@dcloudio/uni-app";
import {
  getAgentList,
  deleteAgent as deleteAgentApi,
  getProvince,
  getCityByProvinceCode,
} from "@/common/api/agentDA/index.js";

// 当前选中的tab
const activeTab = ref("reserve"); // reserve: 储备, formal: 正式
// 搜索关键词
const searchKeyword = ref("");

// 省市选择
const showProvincePicker = ref(false);
const showCityPicker = ref(false);
const selectedProvince = ref("");
const selectedCity = ref("");
const selectedProvinceCode = ref("");
const selectedCityCode = ref("");

// 省市数据
const provinceColumns = ref([]);
const cityColumns = ref([]);
const provinceData = ref([]); // 完整的省份数据
const cityData = ref([]); // 完整的城市数据

// 列表数据
const agentList = ref([]);
const loading = ref(false);
const refreshing = ref(false);
const hasMore = ref(true);

// 分页参数
const pageNum = ref(1);
const pageSize = ref(10);

// 搜索防抖定时器
const searchTimer = ref(null);

onShow((options) => {
  if (options?.activeTab) activeTab.value = options.activeTab;
  initProvinceData();
  initCityData();
  loadAgentList();
});

// 初始化省份数据
const initProvinceData = async () => {
  try {
    const res = await getProvince();
    if (res.code === 0 && res.data) {
      const provinces = res.data;
      // vant picker 需要的数据格式是简单的字符串数组或者包含text属性的对象数组
      provinceColumns.value = [
        "全部省份",
        ...provinces.map((item) => item.label),
      ];

      // 保存完整的省份数据用于查找
      provinceData.value = [
        { text: "全部省份", value: "" },
        ...provinces.map((item) => ({
          text: item.label,
          value: item.value,
        })),
      ];
    }
  } catch (error) {
    console.error("加载省份数据失败:", error);
  }
};

// 初始化城市数据
const initCityData = () => {
  cityColumns.value = ["全部城市"];
  cityData.value = [{ text: "全部城市", value: "" }];
};

// 切换tab
const switchTab = (tab) => {
  if (activeTab.value === tab) return;
  activeTab.value = tab;
  resetList();
  loadAgentList();
};

// 搜索
const onSearch = () => {
  if (searchTimer.value) {
    clearTimeout(searchTimer.value);
  }
  searchTimer.value = setTimeout(() => {
    resetList();
    loadAgentList();
  }, 500);
};

// 打开省份选择器
const openProvincePicker = () => {
  console.log("点击省份选择器");
  showProvincePicker.value = true;
};

// 打开城市选择器
const openCityPicker = () => {
  console.log("点击城市选择器");
  showCityPicker.value = true;
};

// 省份选择确认
const onProvinceConfirm = async (event) => {
  console.log("省份选择确认事件:", event);
  const selectedText = event.detail ? event.detail.value : event.value;
  console.log("选中的省份文本:", selectedText);

  // 根据选中的文本找到对应的省份数据
  const selectedItem = provinceData.value.find(
    (item) => item.text === selectedText
  );

  selectedProvince.value = selectedText;
  selectedProvinceCode.value = selectedItem ? selectedItem.value : "";
  showProvincePicker.value = false;

  // 重置城市选择
  selectedCity.value = "";
  selectedCityCode.value = "";

  // 更新城市数据
  if (selectedProvinceCode.value) {
    try {
      const res = await getCityByProvinceCode({
        provinceCode: selectedProvinceCode.value,
      });
      if (res.code === 0 && res.data) {
        const cities = res.data;
        cityColumns.value = ["全部城市", ...cities.map((item) => item.label)];
        cityData.value = [
          { text: "全部城市", value: "" },
          ...cities.map((item) => ({
            text: item.label,
            value: item.value,
          })),
        ];
      }
    } catch (error) {
      console.error("加载城市数据失败:", error);
    }
  } else {
    cityColumns.value = ["全部城市"];
    cityData.value = [{ text: "全部城市", value: "" }];
  }

  // 重新加载数据
  resetList();
  loadAgentList();
};

// 省份选择取消
const onProvinceCancel = () => {
  showProvincePicker.value = false;
};

// 城市选择确认
const onCityConfirm = (event) => {
  console.log("城市选择确认事件:", event);
  const selectedText = event.detail ? event.detail.value : event.value;
  console.log("选中的城市文本:", selectedText);

  // 根据选中的文本找到对应的城市数据
  const selectedItem = cityData.value.find(
    (item) => item.text === selectedText
  );

  selectedCity.value = selectedText;
  selectedCityCode.value = selectedItem ? selectedItem.value : "";
  showCityPicker.value = false;

  // 重新加载数据
  resetList();
  loadAgentList();
};

// 城市选择取消
const onCityCancel = () => {
  showCityPicker.value = false;
};

// 重置列表
const resetList = () => {
  agentList.value = [];
  pageNum.value = 1;
  hasMore.value = true;
};

// 加载代理商列表
const loadAgentList = async () => {
  if (loading.value) return;

  loading.value = true;

  try {
    const params = {
      limit: pageNum.value,
      size: pageSize.value,
      partnershipStatus: activeTab.value === "reserve" ? "2" : "1", // reserve: 储备, formal: 正式
      name: searchKeyword.value,
      province: selectedProvinceCode.value,
      city: selectedCityCode.value,
    };

    const res = await getAgentList(params);

    if (res.code === 0) {
      const newList = res.data.list || [];

      if (pageNum.value === 1) {
        agentList.value = newList;
      } else {
        agentList.value = [...agentList.value, ...newList];
      }

      // 判断是否还有更多数据
      hasMore.value = newList.length === pageSize.value;
    } else {
      uni.showToast({
        title: res.msg,
        icon: "none",
      });
    }
  } catch (error) {
    console.error("加载代理商列表失败:", error);
  } finally {
    loading.value = false;
    refreshing.value = false;
  }
};

// 加载更多
const loadMore = () => {
  if (!hasMore.value || loading.value) return;
  pageNum.value++;
  loadAgentList();
};

// 下拉刷新
const onRefresh = () => {
  refreshing.value = true;
  resetList();
  loadAgentList();
};

// 新增代理商
const addAgent = () => {
  uni.navigateTo({
    url: "/subPackage/agentDA/add",
  });
};

// 编辑代理商
const editAgent = (item) => {
  uni.navigateTo({
    url: `/subPackage/agentDA/add?id=${item.id}&mode=edit`,
  });
};

// 删除代理商
const deleteAgent = (item) => {
  uni.showModal({
    title: "提示",
    content: "确定要删除该代理商吗？",
    success: async (res) => {
      if (res.confirm) {
        try {
          const result = await deleteAgentApi([item.id]);
          if (result.code === 0) {
            uni.showToast({
              title: "删除成功",
              icon: "success",
            });
            // 重新加载列表
            resetList();
            loadAgentList();
          } else {
            uni.showToast({
              title: result.msg,
              icon: "none",
            });
          }
        } catch (error) {
          console.error("删除代理商失败:", error);
        }
      }
    },
  });
};

// 查看详情
const goToDetail = (item) => {
  const detailPage = activeTab.value === "reserve" ? "detail" : "detailZ";
  uni.navigateTo({
    url: `/subPackage/agentDA/${detailPage}?id=${item.id}`,
  });
};
</script>

<style lang="scss" scoped>
.empty {
  width: 230rpx;
  height: 200rpx;
  margin: 26vh auto 20rpx;
  display: flex;
}
.empty-text {
  color: var(---white, #fff);
  text-align: center;

  /* 点文本-加粗/12pt bold */
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 600;
  line-height: 20px; /* 166.667% */
}
.agent-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 20;
}

.tab-search-row {
  display: flex;
  align-items: center;
  padding: 30rpx 20rpx;
  background: rgba(255, 255, 255, 0.9);

  /* 下层投影 */
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.12);
}

.tab-container {
  display: flex;
  padding: 6rpx;
}

.tab-item {
  display: flex;
  padding: 6rpx 22rpx;
  justify-content: center;
  align-items: center;
  gap: 10px;
  font-size: 28rpx;
  color: #4068f5;
  transition: all 0.3s;
  border-radius: 52rpx;
  background: #fff;
  margin-right: 10rpx;

  &.active {
    background: #4285f4;
    color: #fff;
  }
}

.search-container {
  flex: 1;
  background: #fff;
  border-radius: 50rpx;
  overflow: hidden;
  display: flex;
  align-items: center;
  border: 1px solid #4068f5;
  .search-text {
    width: 100rpx;
    height: 64rpx;
    flex-shrink: 0;
    border-radius: 70rpx;
    background: #4068f5;
    color: #fff;

    /* 点文本-常规/14pt regular */
    font-family: "PingFang SC";
    font-size: 28rpx;
    font-style: normal;
    font-weight: 400;
    text-align: center;
    line-height: 64rpx;
    margin-left: 20rpx;
  }
}

.picker-row {
  display: flex;
  margin-top: 20rpx;
  margin-left: 20rpx;
  margin-bottom: 20rpx;
}

.picker-item {
  display: flex;
  width: 180rpx;
  height: 48rpx;
  justify-content: center;
  align-items: center;
  gap: 2px;
  flex-shrink: 0;
  border-radius: 48rpx;
  background: rgba(255, 255, 255, 0.9);

  /* 下层投影 */
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.12);
  margin-right: 20rpx;
  color: #8d9094;
  /* 点文本-常规/14pt regular */
  font-family: "PingFang SC";
  font-size: 28rpx;
  font-style: normal;
  font-weight: 400;
}
.list-wrapper {
  flex: 1;
  overflow-y: hidden;
  padding: 0 20rpx;
}
// .list-container {
//   height: 100%;
// }

.agent-item {
  margin-bottom: 20rpx;
  border-radius: 20rpx;
  background: rgba(255, 255, 255, 0.9);

  /* 下层投影 */
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.12);
}

.agent-content {
  display: flex;
  align-items: center;
  padding: 30rpx;
}

.agent-info {
  flex: 1;
}

.company-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
}

.location {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.contact {
  font-size: 26rpx;
  color: #666;
}

.detail-btn {
  color: #fff;

  /* 点文本-常规/10pt regular */
  font-family: "PingFang SC";
  font-size: 20rpx;
  font-style: normal;
  font-weight: 400;
  line-height: 36rpx; /* 180% */
  display: inline-flex;
  padding: 2rpx 16rpx;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 130rpx;
  background: linear-gradient(180deg, #52acff -12%, #263af0 117%);
  margin-top: 74rpx;
}

.swipe-actions {
  display: flex;
  height: 100%;
  width: 128rpx;
  margin-left: 1rpx;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 64rpx;
  height: 100%;
  font-size: 20rpx;
  color: #fff;

  &.edit-btn {
    background: #0d6ce4;
  }

  &.delete-btn {
    background: #fc474c;
    border-radius: 0 20rpx 20rpx 0;
  }

  text {
    margin-top: 8rpx;
  }
}

.loading-more,
.no-more {
  text-align: center;
  padding: 40rpx;
  font-size: 26rpx;
  color: #999;
}

.empty-state {
  padding: 100rpx 0;
}

.float-btn {
  box-sizing: border-box;
  width: 94%;
  position: fixed;
  bottom: 40rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 40rpx;
  background: #4285f4;
  color: #fff;
  border-radius: 8rpx;
  font-size: 28rpx;
  box-shadow: 0 4rpx 20rpx rgba(66, 133, 244, 0.3);
  z-index: 100;

  text {
    margin-left: 12rpx;
  }
}
</style>
