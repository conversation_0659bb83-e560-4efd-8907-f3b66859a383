import {
  http
} from '@/common/request/index.js'; // 局部引入

const api = {
  getAgentList: '/bdm/agent/page',
  addAgent: '/bdm/agent/add',
  editAgent: '/bdm/agent/update',
  deleteAgent: '/bdm/agent/delete',
  info: '/bdm/agent/info',
  getProvince: '/magic-api/common/getProvince',
  getCityByProvinceCode: '/magic-api/common/getCityByProvinceCode',
  dictionaryDetail: '/system/dictionary-detail',
  getProductSelect: '/base/product/getProductSelect',
  getCode: '/system/code-rule/generate',
}

// 获取代理商列表
export const getAgentList = (params) => {
  // 模拟API调用
  return http.get(api.getAgentList, { params });
}

// 新增代理商
export const addAgent = (data) => {
  return http.post(api.addAgent, data);
}

// 编辑代理商
export const editAgent = (data) => {
  return http.post(api.editAgent, data);
}

// 删除代理商
export const deleteAgent = (data) => {
  return http.post(api.deleteAgent, data);
}

// 获取代理商详情
export const getAgentDetail = (params) => {
  return http.get(api.info, { params });
}

export const getProvince = (params) => {
  // 省
  return http.get(api.getProvince, { params });
}

export const getCityByProvinceCode = (params) => {
  // 市
  return http.get(api.getCityByProvinceCode, { params });
}

// 获取字典详情
export const dictionaryDetail = (params) => {
  return http.get(api.dictionaryDetail, { params });
}
// 意向产品
export const getProductSelect = (params) => {
  return http.get(api.getProductSelect, { params });
}
// 编码
export const getCode = (params) => {
  return http.get(api.getCode, { params });
}
