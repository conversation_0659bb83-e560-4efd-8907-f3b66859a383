<template>
  <view class="selectClient-panel">
    <view class="page-content">
      <view class="top-panel">
        <view class="search-panel">
          <p-search
            placeholderText="请输入客户名称"
            @searchSumbit="acceptSearch"
          ></p-search>
        </view>
      </view>
      <view class="bottom-panel">
        <view class="bottom-panel-left">
          <van-collapse v-if="isActive" :value="activeNames" @change="onChange">
            <van-collapse-item
              v-for="(item, deptIndex) in deptItems"
              :key="deptIndex"
              :title="item.departmentName"
              :name="item.id"
              :class="{
                'active-collapse-item': activeCollapseIndex === item.id,
              }"
            >
              <image
                v-if="activeCollapseIndex === item.id"
                class="collapse-icon"
                src="/static/image/icon/Lineactive.png"
                mode=""
              >
              </image>
              <view class="van-tree-select__content">
                <view
                  v-for="(doctor, doctorIndex) in item.children"
                  :key="doctorIndex"
                  class="van-ellipsis"
                  :class="{
                    'active-ellipsis':
                      activeDeptIndex === item.id &&
                      activeDoctorIndex === doctor.id,
                  }"
                  @click="handleDoctorClick(item, doctor)"
                >
                  <text>{{ doctor.departmentName }}</text>
                </view>
              </view>
            </van-collapse-item>
          </van-collapse>
        </view>
        <view class="bottom-panel-right">
          <view
            class="doctorInfo"
            v-for="(item, index) in doctorList"
            :key="index"
            @click="onClickItem(item)"
            :class="{
              'active-doctorInfo': activeTreeIndex === item.id,
            }"
          >
            <text>{{ item.name }}</text>
            <text class="professionalName">{{ item.professionalName }}</text>
          </view>
        </view>
      </view>
      <view class="action-buttons">
        <view class="button-group">
          <view class="action-button" @tap="showAddDepartment">新增科室</view>
          <view class="action-button" @tap="goToAddDoctor">新增专家</view>
        </view>
      </view>
    </view>
    <p-tabbar tabbarSelect="work"></p-tabbar>

    <!-- 新增科室弹窗 -->
    <van-popup
      :show="showDepartmentPopup"
      @close="closeDepartmentPopup"
      position="center"
      round
    >
      <view class="department-popup">
        <van-tree-select
          :items="deptTreeData"
          :main-active-index="deptMainActiveIndex"
          :active-id="deptActiveId"
          @click-nav="onDeptClickNav"
          @click-item="onDeptClickItem"
        ></van-tree-select>
        <view class="popup-footer">
          <van-button type="primary" block round @click="saveDepartment"
            >保存</van-button
          >
        </view>
      </view>
    </van-popup>
  </view>
</template>

<script setup>
import { ref, reactive } from "vue";
import { onLoad, onShow } from "@dcloudio/uni-app";
import { deptAdd, deptList, deptTree, dockerList } from "@/common/api/sign";
import { useVisitStore } from "@/common/store/visit";
import { allTree } from "@/common/api/home";
import { getClientInfoById } from "/common/api/business/index.js";

// 新增科室相关
// 记录当前选中的 van-ellipsis 所在的父级索引和自身索引
const activeTreeIndex = ref("");
const activeDeptIndex = ref("");
const activeDoctorIndex = ref("");
const isActive = ref(true);
// 记录当前高亮的 van-collapse-item 索引
const activeCollapseIndex = ref(-1);
const activeNames = ref([]);
const doctorList = ref([]);
const showDepartmentPopup = ref(false);
const deptTreeData = ref([]);
const deptMainActiveIndex = ref(0);
const deptActiveId = ref();
const deptActiveIndex = ref(0);
const visitStore = useVisitStore();
// 科室和医生数据
const deptItems = ref([]);
const items = ref([]);
const loading = ref(false);
const keyword = ref("");
const deptClick = (dept, index) => {
  deptActiveIndex.value = index;
  visitStore.setClientDept(null);
  const deptChildren = dept.children || [];
  const deptData = deptChildren.map((dept) => {
    return {
      ...dept,
      text: dept.departmentName, // 科室名称
      id: dept.departmentId, // 科室ID
      disabled: false,
    };
  });
  items.value = deptData;
  mainActiveIndex.value = 0;
};

// 显示新增科室弹窗
const showAddDepartment = () => {
  allTree()
    .then((res) => {
      if (res.code === 0 && res.data) {
        // 转换数据为tree-select需要的格式
        deptTreeData.value = res.data.map((dept) => ({
          ...dept,
          text: dept.name,
          id: dept.id,
          children:
            dept.children?.map((child) => ({
              ...child,
              text: child.name,
              id: child.id,
            })) || [],
        }));
        showDepartmentPopup.value = true;
      } else {
        uni.showToast({
          title: "获取科室列表失败",
          icon: "none",
        });
      }
    })
    .catch((err) => {
      console.error("获取科室列表错误:", err);
      uni.showToast({
        title: "网络异常，请稍后重试",
        icon: "none",
      });
    });
};
// 处理 van-ellipsis 点击事件
const handleDoctorClick = (item, doctor) => {
  activeDeptIndex.value = item?.id;
  activeDoctorIndex.value = doctor?.id;
  activeCollapseIndex.value = item?.id;
  console.log(item, doctor, "-------------------------------");
  visitStore.setClientDept(doctor);
  // 使用科室ID获取医生列表
  dockerList({
    terminalId: visitStore.$state.clientShop?.id,
    departmentId: doctor?.id,
    dockerName: keyword.value,
  })
    .then((res) => {
      loading.value = false;
      console.log("科室医生数据:", res);
      doctorList.value = res.data || [];
    })
    .catch((err) => {
      loading.value = false;
      console.error("获取医生列表错误:", err);
      uni.showToast({
        title: "网络异常，请稍后重试",
        icon: "none",
      });
    });
};
const onChange = (event) => {
  activeNames.value = event.detail;
  console.log("当前展开的项:", event.detail);
};
// 关闭新增科室弹窗
const closeDepartmentPopup = () => {
  showDepartmentPopup.value = false;
  deptMainActiveIndex.value = 0;
  deptActiveId.value = null;
};

// 点击科室树的导航项
const onDeptClickNav = ({ detail = {} }) => {
  deptMainActiveIndex.value = detail.index;
};

// 点击科室树的选项
const onDeptClickItem = ({ detail = {} }) => {
  deptActiveId.value = detail.id;
};

const getActiveDeptId = () => {
  let item;
  // 获取当前选中的科室ID
  deptTreeData.value.forEach((dept) => {
    if (dept.children && dept.children.length > 0) {
      dept.children.forEach((child) => {
        if (child.id === deptActiveId.value) {
          item = child;
        }
      });
    }
  });
  return item;
};
// 保存科室
const saveDepartment = () => {
  if (!deptActiveId.value) {
    uni.showToast({
      title: "请选择科室",
      icon: "none",
    });
    return;
  }

  const item = getActiveDeptId();
  isActive.value = false;
  deptAdd({
    departmentId: item.id,
    departmentName: item.text,
    parentId: item.parentId,
    parentName: item.parentName,
    terminalId: visitStore.$state.clientShop.id,
    terminalName: visitStore.$state.clientShop.name,
  })
    .then((res) => {
      if (res.code === 0) {
        uni.showToast({
          title: "保存成功",
          icon: "success",
        });
        closeDepartmentPopup();
        getDeptList("add");
      } else {
        uni.showToast({
          title: "保存失败",
          icon: "none",
        });
      }
    })
    .catch((err) => {
      console.error("保存科室错误:", err);
      uni.showToast({
        title: "网络异常，请稍后重试",
        icon: "none",
      });
    });
};

// 跳转到新增专家页面
const goToAddDoctor = () => {
  if (!activeDoctorIndex.value) {
    uni.showToast({
      title: "请先选择科室",
      icon: "none",
    });
    return;
  }
  uni.navigateTo({
    url: "/pages/subVisit/addDoctor/addDoctor",
  });
};

const mainActiveIndex = ref(0); // 左侧选中项的索引

// 点击右侧选项时触发
const onClickItem = (detail) => {
  visitStore.setIsIndex(false);
  // 可能隐藏的问题
  visitStore.setNeedRefresh(true);
  visitStore.setAddressCheck(null);
  activeTreeIndex.value = detail.id;
  visitStore.setClientDoctor(detail);
  visitStore.setClientDept({
    ...visitStore.$state.clientDept,
    departmentId: detail.departmentId,
    departmentName: detail.departmentName,
  });
  visitStore.setAgent(null);
  uni.reLaunch({
    url: "/pages/subVisit/createVisit/createVisit",
  });
};

// 搜索功能
const acceptSearch = (inputKeyword) => {
  isActive.value = false;
  activeDeptIndex.value = "";
  activeDoctorIndex.value = "";
  activeCollapseIndex.value = -1;
  activeNames.value = [];
  if (!inputKeyword) {
    keyword.value = "";
    doctorList.value = [];
    getDeptList();
    isActive.value = true;
    return;
  }
  console.log("搜索关键词:", inputKeyword);
  keyword.value = inputKeyword;
  // 可以根据关键词重新获取数据或者过滤现有数据
  dockerList({
    terminalId: visitStore.$state.clientShop?.id,
    dockerName: inputKeyword,
  })
    .then((res) => {
      loading.value = false;
      console.log("科室医生数据:", res);
      doctorList.value = res.data || [];
      let two = [];
      doctorList.value.forEach((item) => {
        // handleDoctorClick({ id: item.id }, { id: item.departmentId });
        if (item.parentId && !activeNames.value.includes(item.parentId)) {
          activeNames.value.push(item.parentId);
        }
        two.push(item.departmentId);
        console.log("当前展开的项:", activeNames.value);
      });
      two = [...new Set(two)];
      deptItems.value = deptItems.value
        .map((item) => {
          return {
            ...item,
            children: item.children.filter((child) => {
              return two.includes(child.id);
            }),
          };
        })
        .filter((item) => {
          return activeNames.value.includes(item.id);
        });
      console.log("·····················", deptItems.value);

      isActive.value = true;
    })
    .catch((err) => {
      loading.value = false;
      console.error("获取医生列表错误:", err);
      uni.showToast({
        title: "网络异常，请稍后重试",
        icon: "none",
      });
    });
};

// 获取科室和医生数据
const getDeptList = (key) => {
  loading.value = true;
  // 使用医院ID获取科室列表，但不包含医生数据
  deptTree({ terminalId: visitStore.$state.clientShop.id })
    .then((res) => {
      loading.value = false;
      if (res.code === 0 && res.data) {
        // 转换数据格式为tree-select组件需要的格式，但不包含医生数据
        const deptData = res.data.map((dept) => {
          return {
            ...dept,
            text: dept.departmentName, // 科室名称
            id: dept.departmentId, // 科室ID
            disabled: false,
          };
        });
        deptItems.value = deptData;
        if (deptData.length > 0) {
          items.value = deptData[0].children;
          deptClick(deptData[0], 0);
        }
        if (key === "add") {
          activeNames.value = [deptItems.value[0].id];
          isActive.value = true;
          handleDoctorClick(deptItems.value[0], deptItems.value[0].children[0]);
        }
      } else {
        // 处理错误情况
        uni.showToast({
          title: "获取科室列表失败",
          icon: "none",
        });
      }
    })
    .catch((err) => {
      loading.value = false;
      console.error("获取科室列表错误:", err);
      uni.showToast({
        title: "网络异常，请稍后重试",
        icon: "none",
      });
    });
};

// defineExpose({
//   getDeptList: getDeptList,
// });

onLoad(() => {
  getDeptList();
});
onShow(() => {
  // console.log(activeDeptIndex.value, "-----------------");
  activeDeptIndex.value &&
    handleDoctorClick(
      { id: activeDeptIndex.value },
      visitStore.$state.clientDept
    );
});
</script>

<style scoped lang="scss">
@import "@/static/scss/global.scss";
// 高亮的 van-ellipsis 样式
.active-ellipsis {
  color: #4068f5; // 可以根据需求修改文字颜色
}

// 高亮的 van-collapse-item 样式
:deep .active-collapse-item {
  position: relative;
  .van-cell--clickable {
    background: #edf4ff !important;
  }
  .van-cell__title {
    color: #4068f5 !important; // 可以根据需求修改文字颜色
  }
  .collapse-icon {
    width: 6rpx;
    height: 64rpx;
    position: absolute;
    top: 5px;
    left: 16rpx;
  }
}
.bottom-panel {
  display: flex;
  &-left {
    z-index: 5;
    flex: 1;
    height: 82vh;
    overflow-y: auto;
    background-color: #fff;
  }
  &-right {
    flex: 1;
    height: 76vh;
    overflow-y: auto;
    padding: 20rpx 20rpx 0 20rpx;
    .active-doctorInfo {
      color: #4068f5 !important;
    }
    .doctorInfo {
      display: flex;
      padding: 20rpx 24rpx;
      justify-content: space-between;
      align-items: center;
      border-radius: 8px;
      background: #fff;
      color: var(---Gray7, #2f3133);

      /* 点文本-加粗/14pt bold */
      font-family: "PingFang SC";
      font-size: 28rpx;
      font-style: normal;
      font-weight: 600;

      /* 下层投影 */
      box-shadow: 0 4rpx 16rpx 0 rgba(0, 0, 0, 0.12);
      margin-bottom: 20rpx;
      .professionalName {
        /* 点文本-常规/10pt regular */
        font-family: "PingFang SC";
        font-size: 20rpx;
        font-style: normal;
        font-weight: 400;
      }
    }
  }
}
.van-tree-select__content {
  color: var(---Gray6, #8d9094);

  /* 点文本-常规/16pt regular */
  font-family: "PingFang SC";
  font-size: 32rpx;
  font-style: normal;
  font-weight: 400;
  padding-left: 56rpx;
}
.van-ellipsis {
  margin-bottom: 18rpx;
}
:deep .van-collapse-item__content {
  padding: 0;
}
:deep .van-cell__title {
  color: var(---Gray6, #8d9094);
  /* 点文本-常规/16pt regular */
  font-family: "PingFang SC";
  font-size: 32rpx;
  font-style: normal;
  font-weight: 400;
}
.selectClient-panel {
  .page-content {
    @include globalPageStyle();
    display: flex;
    flex-direction: column;
    .top-panel {
      padding: 26rpx 24rpx 18rpx;
      box-sizing: border-box;
      background: rgba(255, 255, 255, 0.9);
      /* 下层投影 */
      box-shadow: 0rpx 4rpx 16rpx 0rpx rgba(0, 0, 0, 0.12);
      display: flex;
      flex-direction: column;
      .menu-panel {
        display: flex;
        justify-content: flex-start;
        margin-bottom: 14rpx;
        @include setlightFont(28rpx, 36rpx, rgba(29, 29, 29, 0.6));
        .item {
          padding-bottom: 6rpx;
          margin-right: 32rpx;
          position: relative;
          &.active {
            @include setBoldFont(28rpx, 36rpx, #303030);
            &::before {
              content: "";
              height: 4rpx;
              background: #4068f5;
              width: 100%;
              position: absolute;
              border-radius: 4rpx;
              left: 0;
              bottom: -4rpx;
              right: 0;
            }
          }
        }
      }
    }
    .action-buttons {
      position: fixed;
      z-index: 6;
      bottom: 150rpx;
      left: 0;
      right: 0;
      padding: 20rpx;

      .button-group {
        display: flex;
        justify-content: space-between;
        padding: 0 80rpx;

        .action-button {
          width: 132rpx;
          height: 40rpx;
          line-height: 40rpx;
          flex-shrink: 0;
          padding: 0 8rpx;
          align-items: center;
          gap: 4rpx;
          border-radius: 10rpx;
          border: 1px solid var(--, #4068f5);
          text-align: right;

          /* 下层投影 */
          box-shadow: 0 4rpx 16rpx 0 rgba(0, 0, 0, 0.12);

          color: var(--, #4068f5);

          /* 点文本-常规/12pt regular */
          font-family: "PingFang SC";
          font-size: 24rpx;
          font-style: normal;
          font-weight: 400;
          background: url("/static/image/icon/add.png") no-repeat 16rpx 10rpx /
            20rpx 20rpx;
        }
      }
    }
  }

  .department-popup {
    width: 85vw;
    padding: 20rpx;
    box-sizing: border-box;
    .popup-header {
      padding: 32rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1rpx solid #ebedf0;

      text {
        font-size: 32rpx;
        font-weight: 500;
        color: #323233;
      }

      .close-icon {
        font-size: 40rpx;
        color: #969799;
      }
    }

    .popup-content {
      padding: 32rpx;

      .department-select {
        margin-bottom: 32rpx;

        .label {
          display: block;
          font-size: 28rpx;
          color: #323233;
          margin-bottom: 16rpx;
        }

        .select-box {
          padding: 20rpx;
          background: #f7f8fa;
          border-radius: 8rpx;

          text {
            font-size: 28rpx;
            color: #969799;
          }
        }
      }
    }

    .popup-footer {
      padding: 32rpx;
      border-top: 1rpx solid #ebedf0;

      .save-button {
        height: 88rpx;
        background: #4068f5;
        border-radius: 44rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #ffffff;
        font-size: 32rpx;
        font-weight: 500;
      }
    }
  }
}
</style>
