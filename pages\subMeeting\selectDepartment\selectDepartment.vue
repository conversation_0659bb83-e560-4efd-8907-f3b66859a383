<template>
	<view class="selectClient-panel">
		<view class="page-content">
<!--			<view class="top-panel">-->
<!--				<view class="search-panel">-->
<!--					<p-search placeholderText="请输入搜索内容" @searchSumbit="acceptSearch"></p-search>-->
<!--				</view>-->
<!--			</view>-->

			<view class="bottom-panel">
        <van-tree-select
            :items="items"
            :main-active-index="mainActiveIndex"
            :active-id="activeIds"
            height="100%"
            @click-nav="onClickNav"
            @click-item="onClickItem"
        />
			</view>
      <view class="order-btn" @click="handleConfirmClick">确认选择</view>
		</view>
		<p-tabbar tabbarSelect="work"></p-tabbar>
	</view>
</template>

<script setup>
import { ref } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { allDept } from "@/common/api/meeting";
import {useVisitStore} from "@/common/store/visit";

const keyword = ref('');
const mainActiveIndex = ref(0)

const items = ref([])
const activeIds = ref([])

const acceptSearch = (inptKeyword) => {
	console.log('搜索条件：keyword', inptKeyword);
	keyword.value = inptKeyword;
};

onLoad((option) => {
  activeIds.value = useVisitStore().$state.selectedDeptIds
  allDept({ id: option.id }).then(res => {
    items.value = convertToVantTree(res.data)
  })
})

const onClickNav = ({ detail = {} }) => {
  mainActiveIndex.value = detail.index || 0
}

const onClickItem = ({ detail = {} }) =>  {

  const _activeIds = activeIds.value
  const index = _activeIds.indexOf(detail.id);
  if (index > -1) {
    _activeIds.splice(index, 1);
  } else {
    _activeIds.push(detail.id);
  }
  activeIds.value = [..._activeIds]
}

const convertToVantTree = (treeData) => {
  let tree = []
  treeData.forEach(item => {
    let children = []
    item.children.forEach(child => {
      children.push({
        id: child.id,
        text: child.name,
        children: []
      })
    })
    tree.push({
      id: item.id,
      text: item.name,
      children: children
    })
  })
  return tree
}
const handleConfirmClick = () => {
  const pages = getCurrentPages();
  const prePage = pages[pages.length - 2];

  prePage.$vm.selectDept(getCheckListByActiveIds());
  uni.navigateBack();
}

const getCheckListByActiveIds = () => {
  let checkList = []
  items.value.forEach(item => {
    if (item.children.length > 0) {
      item.children.forEach(child => {
        if (activeIds.value.includes(child.id)) {
          checkList.push({...child, text: `${item.text}/${child.text}`})
        }
      })
    }
  })
  return checkList
}

</script>

<style scoped lang="scss">
@import '@/static/scss/global.scss';
.selectClient-panel {
	.page-content {
		@include globalPageStyle();
		display: flex;
		flex-direction: column;
		.top-panel {
			padding: 26rpx 24rpx 18rpx;
			box-sizing: border-box;
			background: rgba(255, 255, 255, 0.9);
			/* 下层投影 */
			box-shadow: 0rpx 4rpx 16rpx 0rpx rgba(0, 0, 0, 0.12);
			display: flex;
			flex-direction: column;
			.menu-panel {
				display: flex;
				justify-content: flex-start;
				margin-bottom: 14rpx;
				@include setlightFont(28rpx, 36rpx, rgba(29, 29, 29, 0.6));
				.item {
					padding-bottom: 6rpx;
					margin-right: 32rpx;
					position: relative;
					&.active {
						@include setBoldFont(28rpx, 36rpx, #303030);
						&::before {
							content: '';
							height: 4rpx;
							background: #4068f5;
							width: 100%;
							position: absolute;
							border-radius: 4rpx;
							left: 0;
							bottom: -4rpx;
							right: 0;
						}
					}
				}
			}
			.search-panel {
				::v-deep .search-panel {
					// display: none;
					background: transparent;
					padding: 0;
					box-shadow: none;
				}
			}
		}
		.bottom-panel {
			flex: 1;
			overflow-y: hidden;
			.list-panel {
				box-sizing: border-box;
				padding: 28rpx 20rpx;
				padding-right: 0;
				height: 100%;
				.content-box {
					box-sizing: border-box;
					padding-right: 20rpx;
					height: 100%;
				}

				.client-item {
					@include cardBgCommonStyle();
					height: 84rpx;
					display: flex;
					justify-content: space-between;
					align-items: center;
					width: 100%;
					margin-bottom: 24rpx;
					box-sizing: border-box;
					padding: 20rpx 24rpx;
					.left-panel {
						display: flex;
						flex-direction: column;
						justify-content: space-between;
						.name {
							@include setBoldFont(28rpx, 44rpx, #303030);
						}
					}
				}
			}
		}
    .order-btn {
      margin: 8px auto;
      width: 712rpx;
      height: 64rpx;
      line-height: 64rpx;
      border-radius: 8rpx;
      background: linear-gradient(180deg, #52acff -12%, #263af0 117%);
      @include setlightFont(24rpx, 64rpx, #fff);
      text-align: center;
    }
	}
}
</style>
