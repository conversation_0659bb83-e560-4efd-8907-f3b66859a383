<template>
  <view class="visitLog-panel">
    <view class="page-content">
      <view
        class="visit-target"
        :style="{ height: visitType != '3' ? '124rpx' : '88rpx' }"
      >
        <view class="infos" v-if="visitType != '3'">
          <text class="name">{{ clientDoctorInfo.name }}</text>
          <text class="job">{{ clientDoctorText }}</text>
        </view>
        <view class="infos" v-else>
          <text class="name"
            >{{ agent.agentName }}/{{ agent.agentEmployeeName }}</text
          >
        </view>
        <text class="address" v-if="visitType != '3'">{{
          clientShop.address
        }}</text>
      </view>
      <view class="remark-panel">
        <view class="title">
          <text style="color: #f00">*</text>
          <text>拜访目的：</text>
        </view>
        <view class="content">
          <uv-textarea
            v-model="visitPurpose"
            placeholder="请输入拜访目的"
            maxlength="200"
            adjustPosition
            border="none"
          ></uv-textarea>
        </view>
      </view>

      <view class="uploadImage-panel">
        <view class="title">拍照上传</view>
        <view class="content alone" :class="{ hasImg: fileList.length > 3 }">
          <view
            class="item"
            @tap="chooseImg"
            :class="{ none: !fileList.length }"
          >
            <image class="icon" src="../static/icon_camera.png" mode=""></image>
            <text>添加图片({{ fileList.length }}/9)</text>
          </view>
          <view class="item" v-for="(item, index) in fileList" :key="index">
            <image :src="item" mode="" class="imgs"></image>
            <image
              @tap="deteleFileList"
              :data-value="item"
              src="../static/icon_item_close.png"
              class="delete-icon"
            ></image>
          </view>
        </view>
      </view>

      <view class="remark-panel">
        <view class="title">
          <text style="color: #f00">*</text>
          <text>拜访总结：</text>
        </view>
        <view class="content">
          <uv-textarea
            v-model="visitSumup"
            placeholder="请填写拜访总结"
            maxlength="200"
            adjustPosition
            border="none"
          ></uv-textarea>
        </view>
      </view>
      <view class="sign-out-btn" @click="throttleedLoadMore">{{
        visitType == "3" ? "完成签到" : "确认签退"
      }}</view>
    </view>
    <p-tabbar tabbarSelect="work"></p-tabbar>
  </view>
</template>

<script setup>
import { ref, computed } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { imageCompress, throttle } from "/utils/utils.js";
import { uploadPic } from "/common/api/file/index.js";
import { useVisitStore } from "@/common/store/visit";
import { saveSignIn, saveCoachedEvaluation } from "@/common/api/sign";
import {
  CLIENT_INFO_PARAMS_KEY,
  SAVE_SIGN_INFO_PARAMS_KEY,
  USER_INFO_KEY,
} from "@/common/const/cache";
const fileList = ref([]);
const visitSumup = ref("");
const visitPurpose = ref("");

const visitStore = useVisitStore();
const visitType = ref(visitStore.$state.visitType); //拜访类型
const UserInfo = uni.getStorageSync(USER_INFO_KEY); // 从缓存里拿用户信息
const salesmanId = UserInfo && UserInfo.id ? UserInfo.id : null; // 业务员 ID，确保不存在时为 null
const clientShop = computed(() => {
  return visitStore.$state.clientShop;
});
const clientDoctorInfo = computed(() => {
  return visitStore.$state.clientDoctor;
});
const agent = computed(() => {
  return visitStore.$state.agent;
});
const clientDoctorText = computed(() => {
  return `${visitStore.$state.clientShop?.name}/${visitStore.$state.clientDept.departmentName}`;
});
const deteleFileList = (event) => {
  const deleteItem = event.currentTarget.dataset.value; // 假设是字符串 ID
  fileList.value = fileList.value.filter((item) => item !== deleteItem);
};

const chooseImg = () => {
  if (fileList.value.length < 9) {
    wx.chooseMedia({
      count: 9 - fileList.value.length, // 剩余可上传的图片数量
      mediaType: ["image"],
      sizeType: ["compressed"], // 可以指定是原图还是压缩图，默认二者都有
      success: function (res) {
        uni.showLoading({
          title: "图片上传中",
        });
        console.log(res, "----res");
        const tempFilePaths = res.tempFiles.map((file) => file.tempFilePath);

        // 使用 Promise.all 并行处理所有图片
        const processPromises = tempFilePaths.map((tempFilePath) => {
          return imageCompress(tempFilePath).then((compressedPath) =>
            uploadPic(compressedPath)
          );
        });

        Promise.all(processPromises)
          .then((results) => {
            uni.hideLoading();
            results.forEach((res) => {
              console.log("图片接口回参", res);
              const result = JSON.parse(res);
              const { code, data } = result;

              if (code === 0) {
                fileList.value.push(data[0].fileUrl);
              } else {
                uni.showToast({
                  title: "部分图片上传失败",
                  icon: "none",
                  duration: 2000,
                });
              }
            });
          })
          .catch((error) => {
            console.error("图片上传失败:", error);
            uni.hideLoading();
            uni.showToast({
              title: "图片上传失败",
              icon: "none",
              duration: 2000,
            });
          });
      },
    });
  } else {
    uni.showToast({
      title: "上传图片已达到上限",
      icon: "none",
      duration: 2000,
    });
  }
};

const isManage = () => {
  const UserInfo = uni.getStorageSync(USER_INFO_KEY);
  return UserInfo.posts[0].code == "10002";
};

const saveSignInFun = async () => {
  if (!visitPurpose.value) {
    uni.showToast({
      title: "请填写拜访目的",
      icon: "none",
    });
    return;
  }
  if (fileList.value.length === 0) {
    uni.showToast({
      title: "请先拍照上传图片",
      icon: "none",
    });
    return;
  }
  if (!visitSumup.value) {
    uni.showToast({
      title: "请填写拜访总结",
      icon: "none",
    });
    return;
  }

  const params = {
    ...visitStore.$state.signoutParams,
    imageIds: fileList.value.join(","),
    visitPurpose: visitPurpose.value,
    visitSumup: visitSumup.value,
  };
  if (visitType.value == "3") {
    const tem = {
      imageIds: fileList.value.join(","),
      visitPurpose: visitPurpose.value,
      visitSumup: visitSumup.value,
    };
    tem.visitType = 3;
    tem.id = visitStore.$state.agentEmployee?.id;
    saveCoachedEvaluation(tem).then((res) => {
      if (res.code == 0) {
        uni.showToast({
          title: "签到成功",
          icon: "none",
        });
        visitStore.setActiveTab(1);
        setTimeout(() => {
          uni.reLaunch({
            url: "/pages/subVisit/signRecord/signRecord",
          });
        }, 2000);
      } else {
        uni.showToast({
          title: "签到失败",
          icon: "none",
        });
      }
    });
  } else {
    saveSignIn(params).then((res) => {
      if (res.code == 0) {
        uni.showToast({
          title: "签退成功",
          icon: "none",
          duration: 2000,
        });
        visitStore.setActiveTab(1);
        setTimeout(() => {
          uni.reLaunch({
            url: "/pages/subVisit/signRecord/signRecord",
          });
        }, 2000);
      } else {
        uni.showToast({
          title: "签退失败",
          icon: "none",
        });
      }
    });
  }
};

const throttleedLoadMore = throttle(saveSignInFun, 3000);
</script>

<style scoped lang="scss">
@import "@/static/scss/global.scss";
.visitLog-panel {
  .page-content {
    @include globalPageStyle();
    box-sizing: border-box;
    padding: 28rpx 20rpx;
    overflow-y: auto;
    .visit-target {
      @include cardBgCommonStyle();
      padding: 20rpx 26rpx;
      .infos {
        display: flex;
        align-items: center;
      }
      .name {
        @include setBoldFont(28rpx, 44rpx, #1d1d1d);
        margin-right: 18rpx;
      }
      .address,
      .job {
        @include setBoldFont(20rpx, 36rpx, #8d9094);
        @include ellipsisBasic(1);
      }
    }
    .visit-type,
    .visit-objective {
      margin-top: 24rpx;
      :deep() {
        .left-content {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          .cell-text {
            @include setBoldFont(28rpx, 44rpx, #1d1d1d);
            .select-txt {
              @include setlightFont(28rpx, 44rpx, rgba(29, 29, 29, 0.6));
            }
          }
        }
        .van-cell {
          --cell-vertical-padding: 0;
          --cell-horizontal-padding: 0;
          --cell-background-color: rgba(255, 255, 255, 0.9);
          @include cardBgCommonStyle();
          padding: 23rpx 25rpx;
        }
      }
    }

    .uploadImage-panel {
      margin-top: 24rpx;
      @include cardBgCommonStyle();
      padding: 22rpx 24rpx 30rpx;
      .title {
        @include setBoldFont(28rpx, 36rpx, #1d1d1d);
        margin-bottom: 28rpx;
        &::before {
          content: "*";
          @include setBoldFont(28rpx, 36rpx, #f00);
        }
      }
      .content {
        width: 100%;
        border-radius: 16rpx;
        overflow: hidden;
        position: relative;
        display: flex;
        flex-wrap: wrap;
        height: 306rpx;
        &.hasImg {
          overflow-y: auto;
        }
        .item {
          margin-right: 20rpx;
          margin-bottom: 20rpx;
          background: #e6ebf0;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-direction: column;
          flex-shrink: 0;
          position: relative;
          &.none {
            width: 100%;
            height: 100%;
          }
          width: 200rpx;
          height: 200rpx;
          border-radius: 16rpx;
          overflow: hidden;
          @include setlightFont(28rpx, 32rpx, rgba(29, 29, 29, 0.6));
          .icon {
            width: 48rpx;
            height: 48rpx;
            margin-bottom: 14rpx;
          }
          .imgs {
            width: 200rpx;
            height: 200rpx;
          }
          .delete-icon {
            width: 34rpx;
            height: 34rpx;
            position: absolute;
            right: 0;
            top: 0;
          }
        }
      }
    }

    .remark-panel {
      margin-top: 24rpx;
      @include cardBgCommonStyle();
      padding: 22rpx 24rpx 30rpx;
      .title {
        @include setBoldFont(28rpx, 36rpx, #1d1d1d);
        margin-bottom: 10rpx;
      }
      .content {
        height: 132rpx;
        border-radius: 16rpx;
        background-color: #e6ebf0;
        :deep() {
          .uv-textarea {
            background-color: transparent !important;
            height: 100%;
            width: 100%;
            box-sizing: border-box;
            padding: 23rpx 25rpx;
            .uv-textarea__field {
              height: 100% !important;
            }
          }
          .textarea-placeholder {
            @include setlightFont(28rpx, 32rpx, rgba(29, 29, 29, 0.6));
          }
        }
      }
    }
    .sign-out-btn {
      margin-top: 74rpx;
      @include commonButtonStyle();
    }
  }
}
</style>
