import {
	defineStore
} from 'pinia';
import {
	getUserInfo,
	getUserDetail,
	login<PERSON>pi,
	login<PERSON><PERSON><PERSON><PERSON>,
	doLogout
} from '../../api/login';
import {
	ROLES_KEY,
	TOKEN_KEY,
	USER_INFO_KEY,
	USER_DETAIL_KEY,
	CAN_ASSISTDEFENSE_KEY
} from '../../const/cache';

import {
	usePermissionStore
} from '../permission/permission';
import {
	isAssistDefenseSupported
} from '/utils/utils.js';

export const useUserStore = defineStore('user', {
	state: () => {
		return {
			// user info
			userInfo: null,
			userDetail: null,
			// token
			token: undefined,
			// roleList
			roleList: [],
		};
	},
	getters: {
		getUserInfo() {
			return this.userInfo || uni.getStorageSync(USER_INFO_KEY) || {};
		},
		getToken() {
			return this.token || uni.getStorageSync(TOKEN_KEY);
		},
		getRoleList() {
			return this.roleList || uni.getStorageSync(ROLES_KEY);
		},

	},
	actions: {
		setToken(info) {
			this.token = info ? info : '';
			uni.setStorageSync(TOKEN_KEY, this.token)
		},
		setUserInfo(info) {
			this.userInfo = info;
			uni.setStorageSync(USER_INFO_KEY, this.userInfo)
		},
		setCanAssistDefense(info) {
			console.log('存储',info);
			uni.setStorageSync(CAN_ASSISTDEFENSE_KEY, info)
		},
		setUserDetail(info) {
			this.userDetail = info;
			uni.setStorageSync(USER_DETAIL_KEY, this.userDetail)
		},
		setRoleList(info) {
			this.roleList = info;
			uni.setStorageSync(ROLES_KEY, this.roleList)
		},
		// 验证码登录
		async login(params) {
			console.log('?????');
			// 调用登录接口
			const res = await loginApi(params);
			//存储token
			this.setToken(res.data.token);

			await this.afterLoginAction();

			uni.showToast({
				title: '登录成功！',
				icon: 'none'
			});
			uni.reLaunch({
				url: "/pages/home/<USER>"
			});
		},
		/**
		 * @description: login
		 */
		async phoneLogin(params) {
			const res = await loginPhoneApi(params);

			//存储token
			this.setToken(res.data.token);

			await this.afterLoginAction();

			uni.showToast({
				title: '登录成功！',
				icon: 'none'
			});
			uni.reLaunch({
				url: "/pages/home/<USER>"
			})
		},
		/**
		 * @description: login登陆后的操作
		 */
		async afterLoginAction() {
			if (!this.getToken) return null;

			// 获取用户的信息
			const userInfo = await this.getUserInfoAction();
			const userDetail = await this.getUserDetailAction(userInfo.id)


			const permissionStore = usePermissionStore();
			await permissionStore.changePermissionCode();


			console.log(userInfo, '-------userInfo');
			console.log(userDetail, '-------userDetail');

		},
		async getUserDetailAction(id) {
			if (!this.getToken || !id) return null;
			const userDetailResult = await getUserDetail(id);
			const {
				data
			} = userDetailResult;
			this.setUserDetail(data)
			return Promise.resolve(data);
		},
		async getUserInfoAction() {
			if (!this.getToken) return null;
			const userInfoResult = await getUserInfo();

			const {
				roles = [],
					data,
			} = userInfoResult;
			this.setCanAssistDefense(isAssistDefenseSupported(data.posts))
			this.setUserInfo(data);
			this.setRoleList(roles);
			return Promise.resolve(data);
		},
		async logout() {
			if (this.getToken) {
				try {
					await doLogout();
					uni.showToast({
						title: '退出登录成功！',
						icon: 'none'
					});
					uni.reLaunch({
					  url: "/pages/login/login"
					})
					this.setToken('');
				} catch {
					console.log('注销Token失败');
				}
			} else {
				uni.reLaunch({
					url: "/pages/login/login"
				})
				this.setToken('');
			}
		},

	}

});
