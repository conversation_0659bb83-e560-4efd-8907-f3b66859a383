<template>
	<view class="add-doctor-page">
		<view class="page-content">
			<van-cell-group inset>
        <van-cell
            class="required"
            title="所属终端"
        >
          <text>{{ terminalName }}</text>
        </van-cell>
        <van-cell
            class="required"
            title="所属科室"
        >
          <text>{{ departmentName }}</text>
        </van-cell>
        <van-cell
            class="required"
            title="姓名"
        >
          <input v-model="formData.name" placeholder="请输入" class="meeting-input" maxlength="30" />
        </van-cell>
        <van-cell
            class="required"
            title="联系方式"
        >
          <input v-model="formData.phone" placeholder="请输入" class="meeting-input" maxlength="30" />
        </van-cell>
        <van-cell
            is-link
            title="职称"
            @tap="showProfessionalPopup"
        >
          <text>{{ formData.professionalName }}</text>
        </van-cell>
        <van-cell
            is-link
            title="职务"
            @tap="showDutyPopup"
        >
          <text>{{ formData.dutyName }}</text>
        </van-cell>
        <van-cell
           title="主治专长"
        >
          <input v-model="formData.specialty" placeholder="请输入" class="meeting-input" maxlength="30" />
        </van-cell>
        <van-cell
            is-link
            title="性别"
            @tap="showSexPopup"
        >
          <text>{{ formData.sexName }}</text>
        </van-cell>
        <van-cell
            is-link
            title="民族"
            @tap="showEthnicityPopup"
        >
          <text>{{ formData.nationalityName }}</text>
        </van-cell>
        <van-cell
            is-link
            title="婚姻状况"
            @tap="showMaritalPopup"
        >
          <text>{{ formData.maritalName }}</text>
        </van-cell>
        <van-cell
            is-link
            title="出生日期"
            @tap="showDatePopup"
        >
          <text>{{ formData.birthDate }}</text>
        </van-cell>
			</van-cell-group>
			<view class="button-wrapper">
				<van-button type="primary" block round @click="saveDoctor">保存</van-button>
			</view>
		</view>
    <uv-action-sheet ref="actionSheet" :actions="options[currentActionField]?.map(o => ({ ...o, name:o.label }))" @select="handleActionSheetSelect"/>
    <uv-datetime-picker ref="datetimePicker" v-model="datetimePickerValue" :min-date="new Date(1970, 1, 1).getTime()"  mode="date" @confirm="datePickerConfirm"></uv-datetime-picker>
	</view>
</template>

<script setup>
import { ref, reactive,computed } from 'vue';
import { onLoad } from '@dcloudio/uni-app';

import {useVisitStore} from "@/common/store/visit";
import {addDoctor} from "@/common/api/sign";
import {dictionaryDetail} from "@/common/api/business";
import dayjs from "@/uni_modules/uv-ui-tools/libs/util/dayjs";

const visitStore = useVisitStore();


const terminalName =  computed(() => {
  return visitStore.$state.clientShop.name
})
const departmentName =  computed(() => {
  return visitStore.$state.clientDept.departmentName
})

const options = ref({
  professional: [],
  duty: [],
  nationality: [],
  sex: [{value: 1,label: '男'},{value: 2,label: '女'}],
  marital: [{value: 1,label: '已婚'},{value: 2,label: '未婚'}]
})

const actionSheet = ref(null);
const currentActionField = ref(null);

const datetimePickerValue = ref(null)
const datetimePicker = ref(null)

// 表单数据
const formData = reactive({
	name: '',
  phone: '',
  professional: '',
  professionalName: '',
	duty: '',
	dutyName: '',
	specialty: '',
	sex: '',
	sexName: '',
  nationality: '',
  nationalityName: '',
	marital: '',
	maritalName: '',
	birthDate: ''
});


// 弹窗显示控制
const showProfessionalPopup = () => {
  currentActionField.value = 'professional';
  actionSheet.value.open()
};

const showDutyPopup = () => {
  currentActionField.value = 'duty';
  actionSheet.value.open()
};

const showSexPopup = () => {
  currentActionField.value = 'sex';
  actionSheet.value.open()
};

const showEthnicityPopup = () => {
  currentActionField.value = 'nationality';
  actionSheet.value.open()
};

const showMaritalPopup = () => {
  currentActionField.value = 'marital';
  actionSheet.value.open()
};

const showDatePopup = () => {
  datetimePicker.value.open()
};


const datePickerConfirm = ({ value }) => {
  formData.birthDate = dayjs(value).format("YYYY-MM-DD");
}

const handleActionSheetSelect = (selectedItem => {
  const { value, label } = selectedItem
  formData[currentActionField.value] = value;
  formData[`${currentActionField.value}Name`] = label;
})

// 保存医生信息
const saveDoctor = () => {
  if(!formData.name) {
    uni.showToast({
      title: '请输入姓名',
      icon: 'none'
    });
    return;
  }
  if(!formData.phone) {
    uni.showToast({
      title: '请输入联系方式',
      icon: 'none'
    });
    return;
  }
  addDoctor({
    ...formData,
    terminalId: visitStore.$state.clientShop.id,
    terminalName: terminalName.value,
    departmentId: visitStore.$state.clientDept.departmentId,
    departmentName: departmentName.value
  }).then(res => {
    if (res.code === 0) {
      uni.showToast({
        title: '保存成功',
        icon: 'success'
      });
      // const pages = getCurrentPages();
      // const prePage = pages[pages.length - 2];
      // prePage.$vm.getDeptList()
      uni.navigateBack();
    } else {
      uni.showToast({
        title: res.msg,
        icon: 'none'
      });
    }
  })
};


onLoad((option) => {
  console.log(visitStore.$state)
  // 职称
  dictionaryDetail({itemId: '1866657636967526402'}).then(res => {
    options.value.professional = res.data.map(item =>({ value: item.code, label: item.name }))
  })
  // 职务
  dictionaryDetail({itemId: '1866655732711567361'}).then(res => {
    options.value.duty = res.data.map(item =>({ value: item.code, label: item.name }))
  })
  // 民族
  dictionaryDetail({itemId: '1858355444867682305'}).then(res => {
    options.value.nationality = res.data.map(item =>({ value: item.code, label: item.name }))
  })

});

</script>

<style scoped lang="scss">
@import '@/static/scss/global.scss';
.add-doctor-page {
  .required {
    :deep(.van-cell__title) {
      &::before {
        content: '*';
        @include setBoldFont(28rpx, 44rpx, #f00);
      }
    }
  }
	.page-content {
		@include globalPageStyle();
		padding: 20rpx;
		.button-wrapper {
			margin-top: 40rpx;
			padding: 0 20rpx;
		}

    :deep() {
      .van-cell-group {
        background-color: rgba(255,255,255,0.9);
        margin: 0;
      }
      .van-cell {
        background-color: inherit;
      }
    }
	}
}
</style>
