<template>
	<view class="p-selectBar">
		<view class="bar-item" :class="{ active: currentSelectIndex == index }" v-for="(item, index) in menuListText" :key="index" @click="triggerBarChange(index)">
			<text class="text-info">{{ item }}</text>
		</view>
	</view>
</template>

<script setup>
import { defineProps, ref, defineEmits } from 'vue';

const props = defineProps({
	menuListText: {
		type: Array, // 数组类型不需要加 `[]`
		required: true
	},
	selectIndex: {
		type: Number, // 直接使用 `Number`
		default: 0
	}
});
const currentSelectIndex = ref(props.selectIndex);
const emits = defineEmits(['triggerBarChange']);
/**
 * @description 处理 tab 切换
 * @param {number} index - 被点击的索引
 */
const triggerBarChange = (index) => {
	if (currentSelectIndex.value !== index) {
		currentSelectIndex.value = index;
		emits('triggerBarChange', index);
	}
};
</script>

<style scoped lang="scss">
@import '@/static/scss/global.scss';
.p-selectBar {
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: space-between;
	align-items: center;
	.bar-item {
		display: flex;
		align-items: center;
		flex-direction: column;
		width: 50%;
		text-align: center;
		@include setlightFont(28rpx, 44rpx, #8d9094);
		&.active {
			@include setBoldFont(28rpx, 44rpx, #303030);
			.text-info {
				border-bottom: 4rpx solid #4068f5;
			}
		}
	}
}
</style>
