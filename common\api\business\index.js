import {
	http
} from '@/common/request/index.js'; // 局部引入

const api = {
	listAccount: '/wechat/lineplan/listAccount',
	businessPage: '/business/account/page',
	getBusinessInfoById: '/business/account/info',
	dictionaryDetail: '/system/dictionary-detail',
	updateBusiness: '/business/account/update',
	getAccountUserList: '/business/account/getAccountUserList',
}

// 获取客户列表接口（路线规划的时候用的）
export const getListAccount = (params) => {
	return http.post(api.listAccount, params)
}

// 客户目录列表(分页)
export const getBusinessPage = (params) => {
	return http.post(api.businessPage, params)
}

// 根据id查询otcAccount信息
export const getClientInfoById = (params) => {
	return http.get(api.getBusinessInfoById +
		`?id=${params}`
	)
}

// 根据id查询otcAccount信息
export const dictionaryDetail = (params) => {
	return http.get(api.dictionaryDetail, {params})
}

// 修改otcAccount
export const updateBusiness = (params) => {
	return http.post(api.updateBusiness, params)
}

// 客户档案地图
export const getAccountUserList = (params) => {
	return http.post(api.getAccountUserList, params)
}
