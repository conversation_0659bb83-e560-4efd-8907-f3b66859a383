<template>
	<view class="search-panel">
		<view class="search" :class="{ 'no-right-border': isShowIcon, 'has-border-color': setColor }">
			<uv-search
				class="v-search-ele"
				:placeholder="placeholderText"
				v-model="keyword"
				placeholderColor="#B2B8C2"
				bgColor="#FFF"
				searchIcon=""
				@search="triggerSearchSumbit"
				:showAction="false"
				@focus="triggerSearchFocus"
				@blur="triggerSearchBlur"
			></uv-search>
		</view>
		<view class="search-button" @click="triggerSearchSumbit" v-if="isShowIcon">
			<uv-icon name="search" color="#FFFFFF" size="40rpx"></uv-icon>
		</view>
	</view>
</template>

<script setup>
import { ref, defineProps, defineEmits, onBeforeMount } from 'vue';

// 定义组件的输入属性
const props = defineProps({
	// 搜索框的占位文字，必填
	placeholderText: {
		type: [String],
		required: true
	},
	// 是否展示右侧的搜索icon
	isShowIcon: {
		type: [Boolean],
		default: true
	},
	fromPage: {
		type: [String],
		default: ''
	}
});

const setColor = ref(false);
const keyword = ref('');
const emits = defineEmits(['searchSumbit']);

const triggerSearchFocus = () => {
	setColor.value = true;
};
const triggerSearchBlur = () => {
	setColor.value = false;
};

// 点击搜索按钮时触发的方法
// 将搜索框当前内容通过事件发送给父组件
const triggerSearchSumbit = () => {
	emits('searchSumbit', keyword.value);
};
const clearSearch = () => {
	keyword.value = '';
	terminalType.value = null;
};
// 暴露方法给父组件
defineExpose({
	clearSearch
});
</script>

<style scoped lang="scss">
@import '@/static/scss/global.scss';
.search-panel {
	background: #fff;
	display: flex;
	justify-content: flex-start;
	.search {
		height: 72rpx;
		background: #fff;
		flex: 1;
		box-sizing: border-box;
		border-radius: 8rpx 0rpx 0rpx 8rpx !important;
		border: 2rpx solid #dcdee2;
		overflow: hidden;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		&.no-right-border {
			border-right-width: 0;
		}
		&.has-border-color {
			border-color: #4068f5;
		}
		:deep() {
			.uv-search__content__input--placeholder {
				@include setlightFont(28rpx, 72rpx, #b2b8c2);
			}
			.uv-search__content__input {
				@include setlightFont(28rpx, 72rpx);
			}
		}
	}
	.search-button {
		border-radius: 0rpx 8rpx 8rpx 0rpx;
		background: #4068f5;
		width: 72rpx;
		height: 72rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}
}
</style>
