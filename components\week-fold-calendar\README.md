## 自定义日历，可切换周/月日历展示，支持手势切换
## 理论上全端支持，App端未经测试
### Props
| 参数 | 说明 | 类型 | 默认值 | 可选值 |
| :---: | :---: | :---: | :---: | :---: |
| dots | 日历数据点 | Array | [ ] | - |
| defaultViewType | 日历视图 | String | month | week |
| allowFuture | 允许选择未来日期 | Boolean | false | true |
| minDate | 限制过去日期 | String | - | yyyy-mm-dd |
| customStyle | 自定义默认样式 | Object | { } | - |
| activeBgColor | 选中日期背景色 | String | #3c9cff | - |
| activeColor | 选中日期颜色 | String | #fff | - |
### Event
| 事件 | 说明 |
| :---: | :---: |
| change | 获取选中的日期 |

#### case

```vue
<template>
	<view>
		<view class="text">—— 限制未来日期，默认展示月日历，自定义样式 ——</view>
		<week-fold-calendar @change="change" :dots="dots" :custom-style="customStyle" active-color="black"></week-fold-calendar>

		<view class="text">—— 限制过去的日期，默认展示月日历 ——</view>
		<week-fold-calendar @change="change" @inited="init" :dots="dots" key-name="aa" min-date="2024-08-10"></week-fold-calendar>

		<view class="text">—— 允许选择未来日期，默认展示周日历 ——</view>
		<week-fold-calendar @change="change" :dots="dots" allow-future default-view-type="week"></week-fold-calendar>
	</view>
</template>

<script>
	import WeekFoldCalendar from '@/components/week-fold-calendar/week-fold-calendar.vue';
	export default {
		components: {WeekFoldCalendar},
		data() {
			return {
				dots: [
					{ date: '2024-07-20', value: 1 },
					{ date: '2024-07-30', value: 10 },
					{ date: '2024-07-31', value: 5 },
					{ date: '2024-08-02', value: 3 },
					{ date: '2024-08-03', value: 7 },
					{ date: '2024-08-04', value: 5 },
					{ date: '2024-08-05', value: 2 },
					{ date: '2024-08-10', value: 4 },
					{ date: '2024-08-20', value: 8, aa: 1 },
					{ date: '2024-08-21', value: 6, aa: 2 },
				],
				customStyle: {
					fontSize:'38rpx',
					color:'red', 
					fontWeight: 'normal',
					background:'#eee',
				}
			}
		},
		methods: {
			change(date) {
				console.log(date);
			},
			init(a) {
				console.log(a);
			}
		}
	}
</script>

<style lang="scss" scoped>
	.text {
		text-align: center;
		margin: 40rpx 0;
		font-size: 24rpx;
		color: #aaa;
	}
</style>
```

### 赞赏-> 如果你觉得本插件解决了你的问题，赠人玫瑰，手留余香~
