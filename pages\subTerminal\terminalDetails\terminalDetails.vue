<template>
  <view class="terminalDetails-panel">
    <view class="page-content">
      <view class="detail-panel">
        <view class="top-panel">
          <text class="title">{{ terminalDetail.name }}</text>
          <text class="address">{{ terminalDetail.address }}</text>
        </view>
        <view class="middle-panel">
          <p-selectBar
            :menuListText="['终端档案', '跟进记录']"
            :selectIndex="barSelectIndex"
            @triggerBarChange="acceptBarChange"
          ></p-selectBar>
        </view>

        <view class="bottom-panel">
          <view class="detail-info" v-if="barSelectIndex === 0">
            <view class="item">
              <text class="left-panel">终端名称</text>
              <text class="right-panel">{{ terminalDetail.name }}</text>
            </view>
            <view class="item">
              <text class="left-panel">详细地址</text>
              <text class="right-panel">{{ terminalDetail.address }}</text>
            </view>
            <!--            <view class="item">-->
            <!--              <text class="left-panel">终端性质</text>-->
            <!--              <text class="right-panel">{{ terminalDetail.propertyName }}</text>-->
            <!--            </view>-->
            <view class="item">
              <text class="left-panel">终端级别</text>
              <text class="right-panel">{{ terminalDetail.levelName }}</text>
            </view>
            <view class="item">
              <text class="left-panel">终端等别</text>
              <text class="right-panel">{{ terminalDetail.gradeName }}</text>
            </view>
            <view class="item">
              <text class="left-panel">客户类型</text>
              <text class="right-panel">{{ terminalDetail.typeName }}</text>
            </view>
          </view>
          <view class="step-panel" v-else>
            <scroll-view
              class="list-panel"
              scroll-y="true"
              @scrolltolower="throttledLoadMore"
              lower-threshold="200"
            >
              <view
                class="step-item"
                v-for="(item, index) in list"
                :key="index"
              >
                <image
                  class="icon"
                  src="../static/icon_radio.png"
                  mode=""
                ></image>
                <view class="right-panel">
                  <text class="time">{{ item.createDate }}</text>
                  <text class="status">{{ item.name }}</text>
                  <text
                    class="desc"
                    v-if="item.summarize"
                    @click="handleSummaryClick(item)"
                    >终端开发总结</text
                  >
                </view>
              </view>
            </scroll-view>
          </view>
        </view>
      </view>
      <!-- <view v-if="barSelectIndex === 0" class="changeDetailBtn" @click="handleEditClick">修改信息</view> -->
      <view
        v-if="barSelectIndex !== 0"
        class="changeDetailBtn"
        @click="handleSubmitClick"
        >提交总结</view
      >
    </view>
    <p-tabbar tabbarSelect="work"></p-tabbar>
  </view>
</template>

<script setup>
import { ref, reactive } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { terminalInfo, terminalrecordPage } from "@/common/api/terminal";
const barSelectIndex = ref(1);
// const terminalDetailsInfo = {
// 	terminalName: '南京市人民医院',
// 	contactPhone: '133****1234',
// 	contactPerson: '周周',
// 	detailedAddress: '兴化市戴窑镇三桥路信用社西1号',
// 	terminalNature: '等级医院/基层医疗/民营医疗',
// 	terminalLevel: '三级/二级/一级/未定级',
// 	terminalGrade: '甲等/乙等/丙等/无等别',
// 	customerType: '医院/基层医疗/卫生机构',
// 	terminalBusiness: '县级医院/其他医院'
// };

const list = ref([]);
const id = ref(null);
const terminalDetail = ref({});
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
});

const handleEditClick = () => {
  uni.navigateTo({
    url: `/pages/subTerminal/terminalEdit/terminalEdit?id=${id.value}`,
  });
};

const handleSummaryClick = ({ id: summaryId }) => {
  uni.navigateTo({
    url: `/pages/subTerminal/terminalEditSumUp/terminalEditSumUp?terminalId=${id.value}&id=${summaryId}`,
  });
};

const handleSubmitClick = () => {
  uni.navigateTo({
    url: `/pages/subTerminal/terminalEditSumUp/terminalEditSumUp?terminalId=${id.value}`,
  });
};

const acceptBarChange = (barIndex) => {
  barSelectIndex.value = barIndex;
  console.log("当前是", barSelectIndex.value);
};

const loading = ref(false);

const throttledLoadMore = () => {
  if (loading.value) return;
  pagination.currentPage++;
  getList();
};

const getList = () => {
  if (loading.value) {
    return;
  }
  loading.value = true;
  terminalrecordPage({ terminalId: id.value })
    .then((res) => {
      list.value = res.data.list.concat(list.value);
    })
    .finally(() => {
      loading.value = false;
    });
};

const getTerminalDetailInfo = () => {
  terminalInfo({
    id: id.value,
  }).then((res) => {
    const address =
      res.data.crmTerminalLocationList &&
      res.data.crmTerminalLocationList.length
        ? res.data.crmTerminalLocationList[0].address
        : "";
    terminalDetail.value = { ...res.data, address };
  });
};
const refresh = () => {
  pagination.currentPage = 1;
  list.value = [];
  getList();
  getTerminalDetailInfo();
};

onLoad((option) => {
  id.value = option.id;
  getList();
  getTerminalDetailInfo();
});

defineExpose({
  refresh: refresh,
});
</script>

<style scoped lang="scss">
@import "@/static/scss/global.scss";
.terminalDetails-panel {
  .page-content {
    @include globalPageStyle();
    padding: 28rpx 24rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    overflow: hidden;
    .detail-panel {
      display: flex;
      flex-direction: column;
      flex: 1;
      height: 0;
      .top-panel {
        height: 136rpx;
        @include cardBgCommonStyle();
        padding: 20rpx 24rpx;
        flex-direction: column;
        display: flex;
        .title {
          @include setBoldFont(28rpx, 44rpx, #1d1d1d);
        }
        .address {
          margin-top: 12rpx;
          @include setlightFont(24rpx, 40rpx, rgba(29, 29, 29, 0.6));
          @include ellipsisBasic(1);
        }
      }
      .middle-panel {
        margin: 24rpx auto 0;
        width: 536rpx;
        @include cardBgCommonStyle();
        border-radius: 48rpx;
        height: 68rpx;
      }
      .bottom-panel {
        flex: 1;
        height: 0;
        margin-top: 24rpx;
        .detail-info,
        .step-panel {
          box-sizing: border-box;
          @include cardBgCommonStyle();
        }
        .detail-info {
          padding: 0 8rpx;
          .item {
            width: 100%;
            box-sizing: border-box;
            padding: 20rpx 28rpx;
            border-bottom: 2rpx solid #ced4db;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .left-panel {
              @include setBoldFont(28rpx, 44rpx, #2f3133);
            }
            .right-panel {
              text-align: right;
              width: 70%;
              //@include ellipsisBasic(1);
              @include setlightFont(24rpx, 40rpx, #2f3133);
            }
          }
          .item:last-child {
            border-bottom: none;
          }
        }
        .step-panel {
          max-height: 824rpx;
          height: 100%;
          overflow-y: auto;
          padding: 46rpx 36rpx;
          .step-item {
            display: flex;
            position: relative;
            margin-bottom: 50rpx;
            height: 128rpx;
            .icon {
              width: 32rpx;
              height: 32rpx;
              margin-right: 32rpx;
            }
            .right-panel {
              display: flex;
              flex-direction: column;
              .time {
                @include setBoldFont(28rpx, 44rpx, #2f3133);
              }
              .status {
                margin-top: 4rpx;
                @include setlightFont(24rpx, 40rpx, #2f3133);
              }
              .desc {
                @include setlightFont(24rpx, 40rpx, #4068f5);
                text-decoration: underline;
              }
            }

            &:not(:first-child)::before {
              content: "";
              width: 4rpx;
              height: 130rpx;
              top: -140rpx;
              background: #ced4db;
              /* 下层投影 */
              box-shadow: 0rpx 4rpx 16rpx 0rpx rgba(0, 0, 0, 0.12);
              position: absolute;
              left: 14rpx;
            }
            &:last-child {
              margin-bottom: 0rpx;
            }
          }
        }
      }
    }

    .changeDetailBtn {
      @include commonSquareButtonStyle();
    }
  }
}
</style>
