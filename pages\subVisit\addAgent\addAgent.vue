<template>
  <view class="add-doctor-page">
    <view class="page-content">
      <van-cell-group inset>
        <van-cell class="required" title="所属公司">
          <text>{{ formData.agentName }}</text>
        </van-cell>
        <van-cell class="required" title="姓名">
          <input
            v-model="formData.name"
            placeholder="请输入"
            class="meeting-input"
            maxlength="30"
          />
        </van-cell>
        <van-cell title="联系方式">
          <input
            v-model="formData.phone"
            placeholder="请输入"
            class="meeting-input"
            maxlength="30"
          />
        </van-cell>
        <van-cell is-link title="性别" @tap="showSexPopup">
          <text>{{ formData.sexName }}</text>
        </van-cell>
        <van-cell is-link title="出生日期" @tap="showDatePopup">
          <text>{{ formData.birthDate }}</text>
        </van-cell>
        <van-cell is-link title="职务" @tap="showDutyPopup">
          <text>{{ formData.positionName }}</text>
        </van-cell>
      </van-cell-group>
      <view class="button-wrapper">
        <van-button type="primary" block round @click="saveDoctor"
          >保存</van-button
        >
      </view>
    </view>
    <uv-action-sheet
      ref="actionSheet"
      :actions="
        options[currentActionField]?.map((o) => ({ ...o, name: o.label }))
      "
      @select="handleActionSheetSelect"
    />
    <uv-datetime-picker
      ref="datetimePicker"
      v-model="datetimePickerValue"
      :min-date="new Date(1970, 1, 1).getTime()"
      mode="date"
      @confirm="datePickerConfirm"
    ></uv-datetime-picker>
  </view>
</template>

<script setup>
import { ref, reactive, computed } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { useVisitStore } from "@/common/store/visit";
import { saveAgentEmployee } from "/common/api/agent/index.js";
import dayjs from "@/uni_modules/uv-ui-tools/libs/util/dayjs";
import { dictionaryDetail } from "@/common/api/business";

const visitStore = useVisitStore();
const options = ref({
  position: [],
  duty: [],
  nationality: [],
  sex: [
    { value: 1, label: "男" },
    { value: 2, label: "女" },
  ],
});

const actionSheet = ref(null);
const currentActionField = ref(null);

const datetimePickerValue = ref(null);
const datetimePicker = ref(null);
const agentKey = ref("");
// 表单数据
const formData = reactive({
  name: "",
  phone: "",
  sex: "",
  sexName: "",
  birthDate: "",
  agentName: "",
  positionName: "",
  position: "",
});

// 弹窗显示控制
const showSexPopup = () => {
  currentActionField.value = "sex";
  actionSheet.value.open();
};
const showDutyPopup = () => {
  currentActionField.value = "position";
  actionSheet.value.open();
};
const showDatePopup = () => {
  datetimePicker.value.open();
};

const datePickerConfirm = ({ value }) => {
  formData.birthDate = dayjs(value).format("YYYY-MM-DD");
};
const handleActionSheetSelect = (selectedItem) => {
  const { value, label } = selectedItem;
  formData[currentActionField.value] = value;
  formData[`${currentActionField.value}Name`] = label;
};

// 保存医生信息
const saveDoctor = () => {
  if (!formData.name) {
    uni.showToast({
      title: "请输入姓名",
      icon: "none",
    });
    return;
  }
  saveAgentEmployee({
    ...formData,
    agentId: visitStore.$state.agent.agentId,
  }).then((res) => {
    if (res.code === 0) {
      uni.showToast({
        title: "保存成功",
        icon: "success",
      });
      uni.navigateBack({ delta: 1 });
    } else {
      uni.showToast({
        title: res.msg,
        icon: "none",
      });
    }
  });
};

onLoad(() => {
  formData.agentName = visitStore.$state.agent.agentName;
  // 职务
  dictionaryDetail({ itemId: "1917401227365228546" }).then((res) => {
    options.value.position = res.data.map((item) => ({
      value: item.value,
      label: item.name,
    }));
  });
});
</script>

<style scoped lang="scss">
@import "@/static/scss/global.scss";
.add-doctor-page {
  .required {
    :deep(.van-cell__title) {
      &::before {
        content: "*";
        @include setBoldFont(28rpx, 44rpx, #f00);
      }
    }
  }
  .page-content {
    @include globalPageStyle();
    padding: 20rpx;
    .button-wrapper {
      margin-top: 50vh;
      padding: 0 20rpx;
    }

    :deep() {
      .van-cell-group {
        background-color: rgba(255, 255, 255, 0.9);
        margin: 0;
      }
      .van-cell {
        background-color: inherit;
      }
    }
  }
}
</style>
