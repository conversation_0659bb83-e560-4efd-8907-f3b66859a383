<template>
  <view class="meetingSpeaker-panel">
    <view class="page-content">
      <view class="top-panel">
        <view class="search-panel">
          <p-search placeholderText="请输入搜索内容" @searchSumbit="acceptSearch"></p-search>
        </view>
      </view>

      <view class="bottom-panel">
        <scroll-view class="list-panel" scroll-y="true" @scrolltolower="throttledLoadMore" lower-threshold="200">
          <view class="content-box">
            <view class="client-item" v-for="(item, index) in list" :key="item.id" @tap="triggerClient(item)">
              <view class="left-panel">
                <text class="name">{{ item.name }}</text>
                <text class="dept">{{ item.deptDocName }}</text>
              </view>
              <view class="right-panel">
                <van-checkbox shape="square" :value="item.checked" ></van-checkbox>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
      <view class="order-btn" @click="handleConfirmClick">确认选择</view>
    </view>
    <p-tabbar tabbarSelect="work"></p-tabbar>
  </view>
</template>

<script setup>
import { ref,reactive } from 'vue';
import {doctorPage} from "@/common/api/meeting";
import { onLoad } from '@dcloudio/uni-app';
import {useVisitStore} from "@/common/store/visit";
const keyword = ref('');

const list = ref([])
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
});


const loading = ref(false)

const getList = () => {
  loading.value = true
  doctorPage({
    name: keyword.value,
    limit: pagination.currentPage,
    size: pagination.pageSize,
  }).then(res => {
    const selectedSpeakerIds = useVisitStore().$state.selectedSpeakerIds
    selectedSpeakerIds && selectedSpeakerIds.length && res.data.list.forEach(item =>{
      if(selectedSpeakerIds.find(id => item.id === id)) {
        item.checked = true
      }
    })
    list.value = list.value.concat(res.data.list)
  }).finally(() => {
    loading.value = false
  })
};


const throttledLoadMore = () => {
  if (loading.value) {
    return
  }

  pagination.currentPage++;
  getList();
};

const acceptSearch = (inptKeyword) => {
  console.log('搜索条件：keyword', inptKeyword);
  pagination.currentPage = 1
  list.value = []
  keyword.value = inptKeyword;
  getList()
};

const menuChange = (e) => {
  activeMenu.value = e.currentTarget.dataset.menuindex;
};

const triggerClient = (item) => {
  item.checked = !item.checked
  if(!item.checked) {
    const selectedSpeakerIds = useVisitStore().$state.selectedSpeakerIds
    const index = selectedSpeakerIds.findIndex(id => id === item.id)
    if(index !== -1) {
      selectedSpeakerIds.splice(index, 1)
    }
  } else {
    useVisitStore().$state.selectedSpeakerIds.push(item.id)
  }
}

const handleConfirmClick = () => {
  const pages = getCurrentPages();
  const prePage = pages[pages.length - 2];

  const selection = list.value.filter(item => item.checked)
  prePage.$vm.selectSpeaker(selection);
  uni.navigateBack();
}

onLoad(() => {
  getList()
})
</script>

<style scoped lang="scss">
@import '@/static/scss/global.scss';
.meetingSpeaker-panel {
  .page-content {
    @include globalPageStyle();
    display: flex;
    flex-direction: column;
    .top-panel {
      padding: 26rpx 24rpx 18rpx;
      box-sizing: border-box;
      background: rgba(255, 255, 255, 0.9);
      /* 下层投影 */
      box-shadow: 0rpx 4rpx 16rpx 0rpx rgba(0, 0, 0, 0.12);
      display: flex;
      flex-direction: column;
      .menu-panel {
        display: flex;
        justify-content: flex-start;
        margin-bottom: 14rpx;
        @include setlightFont(28rpx, 36rpx, rgba(29, 29, 29, 0.6));
        .item {
          padding-bottom: 6rpx;
          margin-right: 32rpx;
          position: relative;
          &.active {
            @include setBoldFont(28rpx, 36rpx, #303030);
            &::before {
              content: '';
              height: 4rpx;
              background: #4068f5;
              width: 100%;
              position: absolute;
              border-radius: 4rpx;
              left: 0;
              bottom: -4rpx;
              right: 0;
            }
          }
        }
      }
      .search-panel {
        ::v-deep .search-panel {
          // display: none;
          background: transparent;
          padding: 0;
          box-shadow: none;
        }
      }
    }
    .bottom-panel {
      flex: 1;
      overflow-y: hidden;
      height: 0;
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      .list-panel {
        box-sizing: border-box;
        padding: 28rpx 20rpx;
        padding-right: 0;
        flex: 1;
        height: 0;
        .content-box {
          box-sizing: border-box;
          padding-right: 20rpx;
          height: 100%;
        }

        .client-item {
          height: 144rpx;
          @include cardBgCommonStyle();
          margin-bottom: 24rpx;
          display: flex;
          justify-content: space-between;
          align-items: center;
          box-sizing: border-box;
          padding: 20rpx 24rpx;
          .left-panel {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            .name {
              @include setBoldFont(28rpx, 44rpx, #303030);
            }
            .dept {
              @include setlightFont(28rpx, 44rpx, rgba(29, 29, 29, 0.6));
            }
          }
        }
        // .empty-step {
        // 	height: 100%;
        // 	width: 100%;
        // }
        // .loading-list-panel {
        // 	height: 100%;
        // 	width: 100%;
        // 	display: flex;
        // 	justify-content: center;
        // 	align-items: center;
        // }
      }
      .create-Speaker {
        margin: 28rpx 0;
        padding: 0 20rpx;
        .btn {
          @include commonSquareButtonStyle();
        }
      }
    }
    .order-btn {
      margin: 0 auto;
      width: 712rpx;
      height: 64rpx;
      line-height: 64rpx;
      border-radius: 8rpx;
      background: linear-gradient(180deg, #52acff -12%, #263af0 117%);
      @include setlightFont(24rpx, 64rpx, #fff);
      text-align: center;
    }
  }
}
</style>
