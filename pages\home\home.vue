<template>
  <view class="home-page">
    <view class="page-content">
      <view
        :style="{ height: `${safeAreaInsetsTop ? safeAreaInsetsTop : 0}px` }"
      ></view>
      <view class="userInfo-panel">
        <userInfo></userInfo>
      </view>
      <view v-if="showEmpty()" class="task-panel">
        <image class="empty" src="/static/image/bgs/empty.png" mode=""></image>
        <view class="empty-text">这里空空如也~</view>
      </view>
      <view v-else class="task-panel">
        <view class="title-panel">
          <view class="icon-box">
            <image
              class="icon"
              src="/static/image/icon/home/<USER>"
              mode=""
            ></image>
          </view>
          <text class="title-text">我的目标</text>
        </view>
        <view class="content">
          <view
            class="item"
            v-for="(item, index) in targetList"
            :key="index"
            @click="handleTargetItemClick(item)"
          >
            <text class="i-index">{{ index + 1 }}</text>
            <text class="i-info">{{ item.taskTypeName }}</text>
            <text class="i-desc">{{ item.frequency }}</text>
            <text class="i-desc">{{ item.completionRatio }}</text>
            <view class="i-button">去完成</view>
            <!-- <view class="i-button finish">已完成</view> -->
          </view>
        </view>
      </view>
    </view>
    <p-tabbar tabbarSelect="home"></p-tabbar>
  </view>
</template>

<script setup>
import userInfo from "/components/userInfo/userInfo.vue";
import { onShow } from "@dcloudio/uni-app";
import { ref } from "vue";
import { getHomeList } from "@/common/api/home";
import { useUserStore } from "@/common/store/user";
import { formatTimestamp } from "/utils/utils.js";
import { useVisitStore } from "@/common/store/visit";
import { getByCurrentTime } from "/common/api/sign/index.js";

const visitStore = useVisitStore();
const safeAreaInsetsTop = ref(0);
const targetList = ref([]);
const currentTime = ref("");
// 用户信息
const salesmanId = ref(""); // 业务员 ID，确保不存在时为 null
const showEmpty = () => {
  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo;
  return !userInfo.posts || userInfo.posts.length === 0;
};

const getList = () => {
  // 初始化时计算 `safeAreaInsetsTop`
  uni.getSystemInfo({
    success: (e) => {
      const custom = uni.getMenuButtonBoundingClientRect(); // 获取菜单按钮信息
      safeAreaInsetsTop.value =
        custom.top + custom.height + (custom.top - e.statusBarHeight);
    },
  });
  console.log("Get Home List");
  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo;
  salesmanId.value = userInfo && userInfo.id ? userInfo.id : null; // 业务员 ID，确保不存在时为 null
  console.log("User Info", userInfo);
  getHomeList({
    userId: userInfo.id,
    postCode: userInfo.posts[0].code,
    deptId: userInfo.departmentId,
  }).then((res) => {
    targetList.value = res.data;
  });
};

onShow(() => {
  getList();
  const timestamp = Date.now();
  currentTime.value = formatTimestamp(timestamp);
});

const visitTime = (type) => {
  visitStore.setVisitType(type);
  // 1 终端拜访,2市场协访,3代理商拜访
  let parmas = {
    createDate: currentTime.value,
    personId: salesmanId.value,
    visitType: type,
  };
  getByCurrentTime(parmas).then((res) => {
    const { code, data } = res;
    if (code == 0 && data) {
      visitStore.setSiginType(2);
      visitStore.setNeedRefresh(true);
      visitStore.setIsIndex(true);
      visitStore.setClientShop({
        id: data.purMerchantId,
        name: data.purMerchantName,
        address: data.destination,
        latitude: data.purMerchantLatitude,
        longitude: data.purMerchantLongitude,
        userId: data.coachedPersonId,
        userName: data.coachedPersonName,
        agentId: data.agentEmployeeId,
        agentName: data.agentEmployeeName,
        createDate: data.createDate,
        locationList: null,
      });
      visitStore.setClientDept({
        departmentId: data.baseHospitalId,
        departmentName: data.baseHospitalName,
      });
      visitStore.setClientDoctor({
        ...visitStore.$state.clientDoctor,
        name: data.doctorName,
        id: data.doctorId,
        init: true,
      });
      visitStore.setAgent({
        agentEmployeeId: data.agentEmployeeId,
        agentEmployeeName: data.agentEmployeeName,
        agentId: data.agentId,
        agentName: data.agentName,
        isAgent: data.agentIsPresent,
      });
      visitStore.setAgentEmployee({
        personName: data.coachedPersonName,
        departNames: data.departNames,
        isScene: data.crmIsPresent,
        personId: data.coachedPersonId,
        id: data.id,
        isEvaluate: data?.isEvaluate,
        personCode: data.coachedErpCode,
      });
      uni.reLaunch({
        url: `/pages/subVisit/createVisit/createVisit`,
      });
    } else {
      if (type == "1") {
        uni.reLaunch({
          url: `/pages/subVisit/selectClientShop/selectClientShop`,
        });
      }
      if (type == "3") {
        uni.reLaunch({
          url: `/pages/subVisit/selectAgent/selectAgent`,
        });
      }
      if (type == "2") {
        uni.reLaunch({
          url: `/pages/subVisit/selectSub/selectSub`,
        });
      }
    }
  });
};
const handleTargetItemClick = (item) => {
  // 1 终端拜访,2市场协访,3代理商拜访
  visitTime(item.taskType);
};
</script>

<style scoped lang="scss">
@import "@/static/scss/global.scss";
.home-page {
  .page-content {
    @include globalPageStyle();
    .userInfo-panel {
      box-sizing: border-box;
      width: 100%;
      padding-left: 30rpx;
    }
    .task-panel {
      margin-top: 28rpx;
      box-sizing: border-box;
      // padding-left: 24rpx;
      .title-panel {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        padding: 0 20rpx;
        .icon-box {
          width: 72rpx;
          height: 72rpx;
          background-color: #fff;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16rpx;
          .icon {
            width: 48rpx;
            height: 48rpx;
          }
        }
        .title-text {
          @include setBoldFont(40rpx, 56rpx, #fff);
        }
      }
      .content {
        padding: 28rpx 20rpx;
        .item {
          @include cardBgCommonStyle();
          width: 100%;
          height: 100rpx;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          box-sizing: border-box;
          padding: 26rpx 16rpx;
          margin-bottom: 28rpx;
          .i-index {
            height: 48rpx;
            width: 48rpx;
            line-height: 48rpx;
            border-radius: 12rpx;
            text-align: center;
            background: #4068f5;
            @include setBoldFont(36rpx, 48rpx, #fff);
            margin-right: 16rpx;
          }
          .i-info {
            @include setBoldFont(28rpx, 36rpx, #2f3133);
            width: 292rpx;
            margin-right: 50rpx;
            @include ellipsisBasic(1);
          }
          .i-desc {
            white-space: nowrap;
            margin-right: 40rpx;
            @include setlightFont(24rpx, 36rpx, rgba(29, 29, 29, 0.6));
          }
          .i-button {
            width: 92rpx;
            height: 44rpx;
            border-radius: 4rpx;
            background: linear-gradient(180deg, #52acff -12%, #263af0 117%);
            @include setlightFont(24rpx, 44rpx, #fff);
            text-align: center;
            &.finish {
              background: #c8c8c8;
            }
          }
        }
      }
    }
    .empty {
      width: 230rpx;
      height: 200rpx;
      margin: 20vh auto 20rpx;
      display: flex;
    }
    .empty-text {
      text-align: center;
      @include setlightFont(28rpx, 36rpx, #2f3133);
    }
  }
}
</style>
