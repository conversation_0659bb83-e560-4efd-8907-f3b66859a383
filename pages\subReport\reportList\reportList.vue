<template>
  <view class="reportList-panel">
    <view class="page-content">
      <view class="top-panel">
        <p-selectBar
          :menuListText="menuListText"
          :selectIndex="barSelectIndex"
          @triggerBarChange="acceptBarChange"
        ></p-selectBar>
      </view>
      <view class="bottom-panel">
        <view v-if="loading" class="loading-icon">
          <uv-loading-icon
            loading-mode="spinner"
            loading-text="加载中..."
          ></uv-loading-icon>
        </view>

        <scroll-view
          class="list-panel"
          scroll-y="true"
          @scrolltolower="throttledLoadMore"
          lower-threshold="200"
        >
          <view class="content-box">
            <view
              class="client-item"
              v-for="(item, index) in planList"
              :key="item.id"
              @tap="triggerDetail(item)"
            >
              <view class="item-top-panel">
                <view class="infos">
                  <view class="l-infos">
                    <text>{{ item.createUserName }}</text>
                    <text class="detp">{{ item.postName }}</text>
                  </view>
                  <text class="time">{{ item.createDate }}</text>
                </view>
                <view class="address">{{ item.departNames }}</view>
              </view>
              <view class="item-bottom-panel">
                <text>{{ item.cycle }}{{ formattedReportText }}</text>
                <text
                  v-if="pageType === 'plan'"
                  class="status"
                  @click.stop="deletePlan(item)"
                  >删除</text
                >
                <!-- <text class="status read">已查看</text> -->
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
      <view class="placeholder-view"></view>
      <view class="create-report">
        <image
          class="icon"
          src="../static/icon_createReport.png"
          mode=""
          @tap="triggerShowBtn"
        ></image>
        <view class="button-group" v-if="isVisibleBtn">
          <view class="create-btn week" @tap="triggerCreate('week')">
            <text>{{ createReportText.week }}</text>
          </view>
          <view class="create-btn" @tap="triggerCreate('month')">
            <text>{{ createReportText.month }}</text>
          </view>
        </view>
      </view>
    </view>
    <p-tabbar tabbarSelect="work"></p-tabbar>
  </view>
</template>

<script setup>
import { ref, computed, reactive } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import dayjs from "@/uni_modules/uv-ui-tools/libs/util/dayjs.js";
import {
  taskmanagementpersionPage,
  taskmanagementpersionreportPage,
  taskmanagementpersionDelete,
} from "@/common/api/task";
import { useUserStore } from "@/common/store/user";
import { getWeekRange, getMonthRange } from "@/utils/utils";
import Dialog from "@vant/weapp/lib/dialog/dialog";
const barSelectIndex = ref(0);
const isVisibleBtn = ref(false);
const pageType = ref(""); // "plan" 工作计划 / "report" 工作报告
// const menuListText = ref([]);

const menuListText = computed(() =>
  pageType.value === "plan" ? ["周计划", "月计划"] : ["周报", "月报"]
);

// 计算 `创建报告` 按钮文本
const createReportText = computed(() => ({
  week: pageType.value === "plan" ? "新建周计划" : "新建周报",
  month: pageType.value === "plan" ? "新建月计划" : "新建月报",
}));

const userStore = useUserStore();
const userInfo = userStore.getUserInfo;

const loading = ref(false);

const refresh = () => {
  pagination.currentPage = 1;
  planList.value = [];
  getList();
};

defineExpose({
  refresh: refresh,
});

const planList = ref([]);

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
});

//  `列表报告文本`
const formattedReportText = computed(
  () =>
    `${
      pageType.value === "plan"
        ? "工作计划"
        : barSelectIndex.value === 0
        ? "工作周报"
        : "工作月报"
    }`
);

onLoad((option) => {
  pageType.value = option.pageType || "plan";
  uni.setNavigationBarTitle({
    title: pageType.value === "plan" ? "工作计划" : "工作报告",
  });

  getList();
});

const throttledLoadMore = () => {
  if (loading.value) return;
  pagination.currentPage++;
  getList();
};

const getList = () => {
  loading.value = true;
  const currBarSelectIndex = barSelectIndex.value;
  const start = dayjs().subtract(3, "month").format("YYYY-MM-DD");
  const end = dayjs().add(3, "month").format("YYYY-MM-DD");

  if (pageType.value === "plan") {
    taskmanagementpersionPage({
      createUserId: userStore.getUserInfo.id,
      startYearMonthDay: start,
      endYearMonthDay: end,
      frequency: barSelectIndex.value + 1,
      limit: pagination.currentPage,
      size: pagination.pageSize,
    })
      .then((res) => {
        if (currBarSelectIndex !== barSelectIndex.value) {
          return;
        }
        planList.value = planList.value.concat(res.data.list);
      })
      .finally(() => {
        loading.value = false;
      });
  } else {
    taskmanagementpersionreportPage({
      createUserId: userStore.getUserInfo.id,
      startYearMonthDay: start,
      endYearMonthDay: end,
      frequency: barSelectIndex.value + 1,
      limit: pagination.currentPage,
      size: pagination.pageSize,
    })
      .then((res) => {
        if (currBarSelectIndex !== barSelectIndex.value) {
          return;
        }
        planList.value = planList.value.concat(res.data.list);
      })
      .finally(() => {
        loading.value = false;
      });
  }
};

const acceptBarChange = (barIndex) => {
  planList.value = [];
  barSelectIndex.value = barIndex;
  refresh();
};
const triggerShowBtn = () => {
  isVisibleBtn.value = !isVisibleBtn.value;
};

const triggerCreate = (type) => {
  triggerShowBtn();
  uni.navigateTo({
    url: `/pages/subReport/createReport/createReport?pageType=${pageType.value}&createType=${type}`,
  });
};

const triggerDetail = (item) => {
  uni.navigateTo({
    url: `/pages/subReport/reportDetails/reportDetails?pageType=${
      pageType.value
    }&createType=${barSelectIndex.value == 0 ? "week" : "month"}&id=${item.id}`,
  });
};

const deletePlan = (item) => {
  uni.showModal({
    title: "提示",
    content: "确定删除该工作计划吗？",
    success: (res) => {
      if (res.confirm) {
        taskmanagementpersionDelete({
          id: item.id,
        }).then((res) => {
          if (res.code === 0) {
            uni.showToast({
              title: "删除成功",
              icon: "none",
            });
            refresh();
          } else {
            uni.showToast({
              title: "删除失败",
              icon: "none",
            });
          }
        });
      } else if (res.cancel) {
        console.log("用户点击取消");
      }
    },
  });
};
</script>

<style scoped lang="scss">
@import "@/static/scss/global.scss";
.placeholder-view {
  width: 100%;
  height: 24rpx;
}
.reportList-panel {
  .page-content {
    @include globalPageStyle();
    display: flex;
    flex-direction: column;
    padding: 28rpx 0rpx;
    .top-panel {
      margin: 0 auto;
      width: 536rpx;
      height: 68rpx;
      @include cardBgCommonStyle();
      border-radius: 48rpx;
    }
    .loading-icon {
      margin-top: 12px;
    }
    .bottom-panel {
      flex: 1;
      overflow-y: hidden;
      .list-panel {
        box-sizing: border-box;
        padding: 24rpx 20rpx;
        padding-right: 0;
        height: 100%;
        .content-box {
          box-sizing: border-box;
          padding-right: 20rpx;
          height: 100%;
        }

        .client-item {
          margin-bottom: 24rpx;
          @include cardBgCommonStyle();
          padding: 20rpx 8rpx;
          &:last-child {
            margin-bottom: 0;
          }
          .item-top-panel {
            padding: 0 16rpx;
            border-bottom: 2rpx dashed #ced4db;
            .infos {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 6rpx;
              .l-infos {
                display: flex;
                align-items: center;
                @include setBoldFont(28rpx, 44rpx, #1d1d1d);
                .detp {
                  margin-left: 14rpx;
                  border-radius: 8rpx;
                  background: #21c369;
                  padding: 1rpx 14rpx;
                  @include setlightFont(20rpx, 26rpx, #fff);
                }
              }
              .time {
                @include setlightFont(24rpx, 40rpx, #8d9094);
              }
            }
            .address {
              @include setlightFont(20rpx, 36rpx, #8d9094);
              margin-bottom: 16rpx;
            }
          }
          .item-bottom-panel {
            padding: 0 16rpx;
            margin-top: 14rpx;
            display: flex;
            justify-content: space-between;
            @include setlightFont(20rpx, 36rpx, #2f3133);
            .status {
              @include setlightFont(20rpx, 36rpx, #fc474c);
              height: 36rpx;
              padding: 0 16rpx;
              border-radius: 40rpx;
              background: #faebeb;
              &.read {
                background: rgba(0, 0, 0, 0.05);
                color: #8d9094;
              }
            }
          }
        }
      }
    }
    .create-report {
      z-index: 999;
      width: 72rpx;
      height: 72rpx;
      padding: 18rpx;
      position: fixed;
      bottom: 170rpx;
      right: 38rpx;
      border-radius: 50%;
      background: linear-gradient(180deg, #52acff -12%, #263af0 117%);
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: visible;
      .icon {
        width: 36rpx;
        height: 36rpx;
        z-index: 1;
      }
      .button-group {
        position: absolute;
        width: 210rpx;
        // height: 112rpx;
        @include cardBgCommonStyle();
        top: -148rpx;
        right: 0;
        .create-btn {
          box-sizing: border-box;
          padding: 12rpx 24rpx;
          @include setlightFont(24rpx, 40rpx, #000);
          display: flex;
          justify-content: center;
          align-items: center;
          white-space: nowrap;
          &::before {
            @include setlightFont(20rpx, 32rpx, #fff);
            content: "月";
            width: 32rpx;
            height: 32rpx;
            border-radius: 6rpx;
            background: linear-gradient(180deg, #52acff -12%, #263af0 117%);
            text-align: center;
            margin-right: 10rpx;
          }
          &.week {
            &::before {
              content: "周";
            }
            border-bottom: 1rpx solid #ced4db;
          }
        }
      }
    }
  }
}
</style>
