import {
	http
} from '@/common/request/index.js'; // 局部引入

const api = {

	list: '/wechat/sign/otcAccountEmployeeList',
	add: '/wechat/sign/otcAccountEmployeeAdd',
	edit: '/wechat/sign/otcAccountEmployeeEdit',
	dicItemList: '/system/dictionary-item/dicItemList',


}

/**
 * 门店员工列表
 */

export const otcAccountEmployeeList = (params) => {
	return http.post(api.list, params)
}
// 新增门店员工

export const otcAccountEmployeeAdd = (params) => {
	return http.post(api.add, params)
}

/**
 * 編輯门店员工
 */
export const otcAccountEmployeeEdit = (params) => {
	return http.post(api.edit, params)
}
// 职位字典
export const dicItemList = (params) => {
	return http.post(api.dicItemList, params)
}
