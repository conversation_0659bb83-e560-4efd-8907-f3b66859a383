<template>
  <uv-popup
    ref="popupRef"
    mode="center"
    :safeAreaInsetBottom="false"
    round="16rpx"
    :closeOnClickOverlay="false"
  >
    <view class="evaluate-panel" v-if="signInfo">
      <view class="title">
        <text>协访评价</text>
        <uv-icon name="close" size="32rpx" @tap="closeVisitLog"></uv-icon>
      </view>
      <view class="content" v-if="signInfo.agentIsPresent == 1">
        <view class="item">
          <view class="name"
            >代理商：<text>{{ signInfo.agentEmployeeName }}</text></view
          >
          <view class="name">综合评价</view>
          <view class="rate">
            <uv-rate v-bind="rateProps" v-model="etEvaluate" readonly />
          </view>
        </view>
      </view>
      <view v-if="signInfo.crmIsPresent == 1">
        <view class="item">
          <view class="name">招商代表：{{ signInfo.coachedPersonName }}</view>
        </view>
        <view class="content" v-if="rateItems.length > 0">
          <view v-for="(item, index) in rateItems" :key="index" class="item">
            <view class="name">{{ item.label }}</view>
            <view class="rate">
              <uv-rate v-bind="rateProps" v-model="item.value" readonly />
            </view>
          </view>
        </view>
      </view>
      <view class="remak-panel" v-if="signInfo.suggestion">
        {{ signInfo.suggestion }}
      </view>
    </view>
  </uv-popup>
</template>

<script setup>
import { ref, defineEmits, onMounted } from "vue";
import { getOtcSignIn } from "/common/api/sign/index.js";

// 默认评分组件配置
const rateProps = {
  activeColor: "#F9BE3A",
  inactiveColor: "#b2b2b2",
  gutter: 8,
  size: "64rpx",
  allowHalf: true,
};

// 弹窗引用和数据存储
const popupRef = ref(null);
const signInfo = ref(null);

const props = defineProps({
  idEvaluate: {
    type: String,
    required: true,
  },
});

// 评分项配置
const rateItems = ref([]);
const etEvaluate = ref(0);

// 事件发射
const emits = defineEmits(["closeEvaluate"]);

// 初始化数据
onMounted(async () => {
  if (props.idEvaluate) {
    try {
      const res = await getOtcSignIn(props.idEvaluate);
      if (res.code === 0 && res.data) {
        if (
          !res.data.suggestion &&
          res.data.agentIsPresent == 0 &&
          res.data.crmIsPresent == 0
        ) {
          uni.showToast({
            title: "暂无评价",
            icon: "none",
            duration: 2000,
          });
          console.error("查询失败");
          return;
        }
        signInfo.value = res.data;
        etEvaluate.value = res.data.etEvaluate;
        rateItems.value = [
          { label: "访前计划", value: res.data.preVisitPlan },
          { label: "工作态度", value: res.data.workAttitude },
          { label: "客情管理", value: res.data.cusRelationshipManagement },
          { label: "陈述利益", value: res.data.statementInterests },
          { label: "综合评价", value: res.data.evaluate },
        ];
        popupRef.value.open();
      } else {
        uni.showToast({
          title: "暂无评价",
          icon: "none",
          duration: 2000,
        });
        console.error("查询失败");
      }
    } catch (error) {
      console.error("获取评价数据失败", error);
    }
  } else {
    uni.showToast({
      title: "暂无评价",
      icon: "none",
      duration: 2000,
    });
  }
});

// 关闭弹窗
const closeVisitLog = () => {
  emits("closeEvaluate");
};
</script>

<style scoped lang="scss">
@import "@/static/scss/global.scss";

.evaluate-panel {
  padding: 28rpx 24rpx;
  box-sizing: border-box;
  width: 710rpx;

  .title {
    display: flex;
    justify-content: space-between;
    @include setBoldFont(28rpx, 36rpx, #1d1d1d);
    align-items: center;
    margin-bottom: 18rpx;
    width: 100%;
  }

  .people {
    @include setlightFont(28rpx, 36rpx, #1d1d1d);

    .name {
      @include setBoldFont(22rpx, 32rpx, #1d1d1d);
    }
  }
  .item {
    margin-bottom: 20rpx;

    .name {
      @include setlightFont(22rpx, 32rpx, #1d1d1d);
    }

    .rate {
      display: flex;
      justify-content: center;
    }
  }

  .remak-panel {
    height: 202rpx;
    overflow-y: auto;
    margin-top: 56rpx;
    border-radius: 16rpx;
    background: #f1f1f1;
    box-sizing: border-box;
    padding: 16rpx 20rpx;
    @include setlightFont(22rpx, 32rpx, #1d1d1d);
  }
}
</style>
