<template>
	<view class="mine-page">
		<view class="page-content">
			<view :style="{ height: `${safeAreaInsetsTop ? safeAreaInsetsTop : 0}px` }"></view>
			<view class="userinfo-panel">
				<userInfo></userInfo>
			</view>
			<view class="setting-panel">
				<van-cell title="" is-link @tap="jumpToSetting">
					<view slot="title" class="left-content">
						<image class="cell-icon" src="/static/image/icon/mine/icon_setting.png" mode=""></image>
						<view class="cell-text">设置</view>
					</view>
				</van-cell>
			</view>
		</view>
		<p-tabbar tabbarSelect="mine"></p-tabbar>
	</view>
</template>

<script setup>
import { onLoad } from '@dcloudio/uni-app';
import { getSafeAreaInsetsTop } from '@/utils/utils.js';
import { ref } from 'vue';
import userInfo from '/components/userInfo/userInfo.vue';

const safeAreaInsetsTop = ref(0);

onLoad(() => {
	safeAreaInsetsTop.value = getSafeAreaInsetsTop();
});
const jumpToSetting = () => {
	uni.navigateTo({
		url: '/pages/subMine/setting/setting'
	});
};
</script>

<style scoped lang="scss">
@import '@/static/scss/global.scss';
.mine-page {
	.page-content {
		@include globalPageStyle();
		.userinfo-panel {
			box-sizing: border-box;
			width: 100%;
			padding-left: 30rpx;
		}
		.setting-panel {
			margin-top: 28rpx;
			box-sizing: border-box;
			padding: 0 20rpx;
			:deep() {
				.left-content {
					display: flex;
					align-items: center;
					justify-content: flex-start;
					.cell-icon {
						width: 48rpx;
						height: 48rpx;
						margin-right: 16rpx;
					}
					.cell-text {
						@include setlightFont(28rpx, 44rpx, #1d1d1d);
					}
				}
				.van-cell {
					--cell-vertical-padding: 0;
					--cell-horizontal-padding: 0;
					@include cardBgCommonStyle();
					padding: 36rpx 20rpx 34rpx 24rpx;
				}
			}
		}
	}
}
</style>
