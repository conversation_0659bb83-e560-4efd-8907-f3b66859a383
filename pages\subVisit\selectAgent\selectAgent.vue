<template>
  <view class="selectClient-panel">
    <view class="page-content">
      <view class="top-panel">
        <view class="search-panel">
          <p-search
            placeholderText="请输入代理商名称"
            @searchSumbit="acceptSearch"
          ></p-search>
        </view>
      </view>
      <!-- <view class="agent-container">
        <view
          v-for="(tab, index) in tabs"
          :key="index"
          :class="['tab-item', { active: activeTab === tab.key }]"
          @click="switchTab(index, tab.key)"
        >
          {{ tab.label }}
        </view>
      </view> -->
      <view v-if="agentList.length === 0" class="task-panel">
        <image class="empty" src="/static/image/bgs/empty.png" mode=""></image>
        <view class="empty-text">暂无数据</view>
      </view>
      <view v-else class="bottom-panel">
        <scroll-view
          class="list-panel"
          scroll-y="true"
          @scrolltolower="throttledLoadMore"
          lower-threshold="200"
        >
          <view class="content-box">
            <view
              class="person-cont"
              v-for="(item, index) in agentList"
              :key="index"
              @tap="triggerClient(item)"
            >
              <text class="tag xue" v-if="item.type === '企业'">企业</text
              ><text class="tag dai" v-if="item.type === '个人'">个人</text
              >{{ item.name }}
            </view>
          </view>
        </scroll-view>
      </view>
      <view v-if="loading" class="uv-picker--loading">
        <uv-loading-icon mode="circle"></uv-loading-icon>
      </view>
      <view class="placeholder-view" v-if="visitType === '3'"></view>
      <p-visit v-if="visitType === '3'"></p-visit>
    </view>
    <p-tabbar tabbarSelect="work"></p-tabbar>
  </view>
</template>

<script setup>
import { onLoad, onShow } from "@dcloudio/uni-app";
import { ref, reactive } from "vue";
import { useVisitStore } from "@/common/store/visit";
import { useUserStore } from "@/common/store/user";
import { getAgentList } from "/common/api/agent/index.js";

const loading = ref(false);
const tabs = [
  { id: 1, label: "企业", key: "business" },
  { id: 2, label: "个人", key: "person" },
];
const activeTab = ref("person");
const activeNames = ref([]);
const pagination = reactive({
  currentPage: 1,
  totalItems: 0,
  pageSize: 20,
});
const keyword = ref("");
const visitStore = useVisitStore();
const userStore = useUserStore();
const visitType = ref(visitStore.$state.visitType);
const userInfo = userStore.getUserInfo;
const agentList = ref([]);
const getList = async (name) => {
  if (loading.value) {
    return;
  }
  loading.value = true;
  try {
    let parmas = {
      limit: pagination.currentPage,
      size: pagination.pageSize,
      agentName: name || "",
      visitType: visitStore.$state.visitType,
    };
    if (visitStore.$state.visitType === "3") {
      parmas.deptId = userInfo.departmentId;
      parmas.postCode = userInfo.posts[0].code;
    } else {
      parmas.terminalId = visitStore.$state.clientShop?.id;
    }
    let { code, data } = await getAgentList(parmas);
    loading.value = false;
    if (code == 0) {
      agentList.value = agentList.value.concat(data.list || []);
    }
  } catch (error) {
    console.error("Error:", error);
  }
};
const triggerClient = (item) => {
  visitStore.setAgent({
    agentId: item.id,
    agentName: item.name,
    isOne: true,
  });
  uni.navigateTo({
    url: `/pages/subVisit/selectAgent/agentChild`,
  });
};
const throttledLoadMore = () => {
  if (loading.value) return;
  pagination.currentPage++;
  getList(keyword.value);
};
const acceptSearch = (inptKeyword) => {
  keyword.value = inptKeyword;
  console.log("搜索条件：keyword", inptKeyword);
  pagination.currentPage = 1;
  agentList.value = [];
  getList(inptKeyword);
};
onLoad(() => {
  getList();
});
onShow(() => {
  visitStore.setActiveTab(0);
});
</script>

<style scoped lang="scss">
@import "@/static/scss/global.scss";
.placeholder-view {
  width: 100%;
  height: 60rpx;
}
.bottom-panel {
  flex: 1;
  overflow-y: hidden;

  .list-panel {
    box-sizing: border-box;
    padding: 28rpx 20rpx;
    padding-right: 0;
    height: 100%;

    .content-box {
      box-sizing: border-box;
      padding-right: 20rpx;
      height: 100%;
    }
  }
}
.person-cont {
  height: 84rpx;
  display: flex;
  align-items: center;
  padding-left: 24rpx;
  border-radius: 16rpx;
  background: #fff;

  /* 下层投影 */
  box-shadow: 0 4rpx 16rpx 0 rgba(0, 0, 0, 0.12);
  margin-bottom: 20rpx;
  color: var(---Gray7, #2f3133);

  /* 点文本-加粗/14pt bold */
  font-family: "PingFang SC";
  font-size: 28rpx;
  font-style: normal;
  font-weight: 600;
  .tag {
    width: 64rpx;
    height: 36rpx;
    margin-right: 20rpx;
    line-height: 36rpx;
    text-align: center;
    /* 点文本-加粗/10pt bold */
    font-family: "PingFang SC";
    font-size: 20rpx;
    font-style: normal;
    font-weight: 400;
    border-radius: 8rpx;
  }
  .xue {
    color: #5287db;
    border: 2rpx solid #5287db;
  }
  .dai {
    color: #51de6b;
    border: 2rpx solid #51de6b;
  }
}
.btn {
  display: flex;
  width: 95vw;
  position: fixed;
  bottom: 170rpx;
  margin-left: 20rpx;
  color: #fff;
  font-family: "PingFang SC";
  font-size: 28rpx;
  font-style: normal;
  font-weight: 600;
  height: 72rpx;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  text-align: center;
  border-radius: 8rpx;
  background: var(--, linear-gradient(180deg, #52acff -12%, #263af0 117%));

  /* 下层投影 */
  box-shadow: 0 4rpx 16rpx 0 rgba(0, 0, 0, 0.12);
}
.scroll-view {
  flex: 1;
  overflow-y: hidden;
  padding: 0 20rpx;
  margin-top: 20rpx;
}
:deep .business-collapse {
  .van-collapse-item {
    border: none;
  }
  .van-cell {
    border-radius: 16rpx;
    margin-bottom: 20rpx;
  }
  .van-cell:after {
    border: none;
  }
  [class*="van-hairline"]::after {
    border: none;
  }
  .van-collapse-item__content {
    background: transparent;
    padding: 0;
  }
  .van-cell__title {
    color: var(---Gray7, #2f3133);

    /* 点文本-加粗/14pt bold */
    font-family: "PingFang SC";
    font-size: 28rpx;
    font-style: normal;
    font-weight: 600;
  }
  .cont {
    height: 64rpx;
    display: flex;
    align-items: center;
    flex-shrink: 0;
    border-radius: 16rpx;
    background: #fff;
    margin-left: 28rpx;
    padding-left: 24rpx;
    color: var(---Gray7, #2f3133);

    /* 点文本-加粗/14pt bold */
    font-family: "PingFang SC";
    font-size: 28rpx;
    font-style: normal;
    font-weight: 600;
    /* 下层投影 */
    box-shadow: 0 4rpx 16rpx 0 rgba(0, 0, 0, 0.12);
    margin-bottom: 20rpx;
  }
}
.agent-container {
  display: flex;
  width: 370rpx;
  height: 68rpx;
  padding: 10rpx 80rpx;
  justify-content: space-between;
  align-items: flex-end;
  flex-shrink: 0;
  margin: 20rpx auto;
  border-radius: 48rpx;
  background: rgba(255, 255, 255, 0.9);

  /* 下层投影 */
  box-shadow: 0 4rpx 16rpx 0 rgba(0, 0, 0, 0.12);
  .tab-item {
    width: 112rpx;
    align-self: stretch;
    color: var(---Gray6, #8d9094);
    text-align: center;

    /* 点文本-常规/14pt regular */
    font-family: "PingFang SC";
    font-size: 28rpx;
    font-style: normal;
    font-weight: 400;
    line-height: 70rpx; /* 157.143% */
  }
  .active {
    color: #303030;
    font-weight: 600;
    border-bottom: 4rpx solid #4068f5;
  }
}
.selectClient-panel {
  .page-content {
    @include globalPageStyle();
    display: flex;
    flex-direction: column;

    .top-panel {
      z-index: 1;
      padding: 26rpx 24rpx 18rpx;
      box-sizing: border-box;
      background: rgba(255, 255, 255, 0.9);
      /* 下层投影 */
      box-shadow: 0rpx 4rpx 16rpx 0rpx rgba(0, 0, 0, 0.12);
      display: flex;
      flex-direction: column;

      .search-panel {
        ::v-deep .search-panel {
          // display: none;
          background: transparent;
          padding: 0;
          box-shadow: none;
        }
      }
    }
  }
  .empty {
    width: 230rpx;
    height: 200rpx;
    margin: 26vh auto 20rpx;
    display: flex;
  }
  .empty-text {
    color: var(---white, #fff);
    text-align: center;

    /* 点文本-加粗/12pt bold */
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px; /* 166.667% */
  }
}
</style>
