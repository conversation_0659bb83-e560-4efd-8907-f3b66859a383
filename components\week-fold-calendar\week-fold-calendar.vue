<template>
	<view class="week-content">
		<view class="controller">
			<view class="today-btn">
				<view v-show="false" class="sub_color fs_12" @click="backToday">回今日</view>
			</view>
			<view class="action">
				<view class="current-current" @tap="openDatetimePicker">
					{{ dateLabel }}
				</view>
			</view>
		</view>

		<view class="calender" :class="[viewType]">
			<view class="week-box">
				<view :key="index" v-for="(item, index) in week" class="week">
					{{ item }}
				</view>
			</view>

			<view class="day-box">
				<view class="placeholder" :class="[changeType]">
					<view
						:key="index"
						v-for="(item, index) in virtually_data"
						class="item"
						:class="{
							in: item.set || item.get,
							before: item.before,
							after: item.after && !allowFuture,
							active: current == item.date && item.type === 'current',
							prev: item.type === 'prev',
							next: item.type === 'next',
							today: item.today
						}"
						:style="[cty]"
					>
						<view>{{ item.day }}</view>
						<view class="dots" v-show="item[keyName]"></view>
					</view>
				</view>

				<view class="days" :class="[changeType]" @touchend="touchend" @touchmove="touchmove" @touchstart="touchstart">
					<view
						:key="index"
						v-for="(item, index) in days"
						class="item"
						:class="{
							in: item.set || item.get,
							before: item.before,
							after: item.after && !allowFuture,
							active: current == item.date && item.type === 'current',
							prev: item.type === 'prev',
							next: item.type === 'next',
							today: item.today
						}"
						:style="[cty]"
						@click="itemClick(item)"
					>
						<view>{{ item.day }}</view>
						<view class="dots" v-show="item[keyName]"></view>
					</view>
				</view>
			</view>
		</view>
		<view class="view-change-btn" @click="changeViewType">
			<view class="open-icon"></view>
		</view>
	</view>
	<uv-datetime-picker ref="datetimePicker" v-model="datetimePickerValue" :minDate="minDataValue" :maxDate="maxDate" mode="year-month" @confirm="confirm"></uv-datetime-picker>
</template>

<script>
import { currentDate, getDaysDifference, formattedDate, getMonthDays, reduMonths, getAppointDate } from './dateManage.js';
const today = currentDate();
let startX = 0;
let endX = 0;

export default {
	props: {
		dots: {
			// 数据点
			type: Array,
			default: () => []
		},
		keyName: {
			// 数据点的key
			type: String,
			default: 'value'
		},
		defaultViewType: {
			// 日历视图
			type: String,
			default: 'week'
		},
		allowFuture: {
			// 允许选择未来日期
			type: Boolean,
			default: false
		},
		minDate: {
			// 过去日期限制
			type: String,
			default: ''
		},
		customStyle: {
			// 自定义默认样式
			type: Object,
			default: () => {}
		},
		activeBgColor: {
			// 自定义选中样式
			type: String,
			default: '#3c9cff'
		},
		activeColor: {
			// 自定义选中样式
			type: String,
			default: '#fff'
		}
	},
	data() {
		return {
			week: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
			virtually_data: [],
			days: [],
			current: currentDate(),
			changeType: '',
			prevDisabled: false,
			nextDisabled: true,
			viewType: '',
			datetimePickerValue: '',
			minDataValue: '',
			maxDate: ''
		};
	},
	watch: {
		dots: {
			immediate: true,
			deep: true,
			handler() {
				this.setCalenderDots();
			}
		},
		current(n) {
			this.$emit('change', n);
		}
	},
	computed: {
		showTodayBtn() {
			return this.current != today;
		},
		dateLabel() {
			return formattedDate(this.current, 'yyyy-mm');

			// if (this.viewType === 'week') {
			// 	return formattedDate(this.current, 'yyyy-mm-dd');
			// } else {
			// 	return formattedDate(this.current, 'yyyy-mm');
			// }
		},
		cty() {
			return {
				...this.customStyle,
				'--active': this.activeColor,
				'--active-bg': this.activeBgColor
			};
		}
	},
	created() {
		this.nextDisabled = !this.allowFuture;
		this.viewType = this.defaultViewType || 'month';
		this.init();
		this.getYearStartAndEndTimestamps();
	},
	methods: {
		confirm(e) {
			// 获取选中的日期
			const selectedDate = new Date(e.value);

			// 格式化日期为 yyyy-mm-dd
			const formattedDate = `${selectedDate.getFullYear()}-${(selectedDate.getMonth() + 1).toString().padStart(2, '0')}-${selectedDate
				.getDate()
				.toString()
				.padStart(2, '0')}`;

			// 设置当前日期为选中的日期
			this.current = formattedDate;

			// 根据视图类型判断，是否切换到相应的视图
			if (this.viewType === 'week') {
				// 如果是 week 视图，则调用 initWeekCalender 方法
				this.initWeekCalender(formattedDate);
			} else {
				// 如果是 month 视图，则调用 initMonthCalender 方法
				this.initMonthCalender(formattedDate);
			}
		},

		getYearStartAndEndTimestamps() {
			const currentDate = new Date();

			// 获取当前年份
			const year = currentDate.getFullYear();

			// 获取当前年份的第一天：1月1日
			const startOfYear = new Date(year, 0, 1); // 0 表示 1 月份
			const endOfYear = new Date(year + 1, 0, 0); // 下一年的第一天，表示当前年份的最后一天

			// 获取时间戳（单位：毫秒）
			const startTimestamp = startOfYear.getTime();
			const endTimestamp = endOfYear.getTime();
			this.minDataValue = startTimestamp;
			this.maxDate = endTimestamp;
		},
		openDatetimePicker() {
			this.$refs.datetimePicker.open();
		},
		// 回今日
		backToday() {
			this.nextDisabled = !this.allowFuture;
			this.init();
		},

		// 日历切换
		handleChange(type) {
			if (type === 'next' && this.nextDisabled) return;
			if (type === 'prev' && this.prevDisabled) return;

			if (this.viewType === 'week') {
				this.switchWeek(type);
			} else {
				this.switchMonth(type);
			}
		},

		// 初始化日历
		init() {
			this.virtually_data = [];
			this.current = today;

			if (this.viewType === 'week') {
				this.initWeekCalender();
			} else {
				this.initMonthCalender();
			}
		},

		// 切换月份
		switchMonth(type) {
			if (this._clock) return;
			this._clock = true;
			this.changeType = type;

			const date = this.getMonth(type);
			this.initMonthCalender(date);

			let _default = date;
			if (!this.allowFuture) {
				const monthDiff = reduMonths(today, date);
				this.nextDisabled = monthDiff <= 0;
				if (getDaysDifference(today, _default) < 0) {
					_default = today;
				}
			}
			if (this.minDate) {
				const cd = new Date(date);
				const firstDay = formattedDate(new Date(cd.getFullYear(), cd.getMonth(), 1));
				const minDiff = getDaysDifference(firstDay, this.minDate);
				this.prevDisabled = minDiff <= 0;
				if (getDaysDifference(date, this.minDate) <= 0) {
					_default = this.minDate;
				}
			}
			this.current = _default;

			// 500ms节流
			setTimeout(() => {
				this.changeType = '';
				this._clock = false;
				this.virtually_data = [...this.__days];
			}, 500);
		},

		// 切换星期
		switchWeek(type) {
			if (this._clock) return;
			this._clock = true;
			this.changeType = type;

			this.initWeekCalender(this.current, type);

			// 500ms节流
			setTimeout(() => {
				this.changeType = '';
				this._clock = false;
				this.virtually_data = [...this.__days];
			}, 500);
		},

		// 日期点击
		itemClick(data) {
			if ((data.after && !this.allowFuture) || data.before || this.current == data.date) return;
			this.current = data.date;
			// 点击其他月份的日期，切换日历
			if (data.type !== 'current') {
				this.handleChange(data.type);
			}
		},

		// 切换日历视图
		changeViewType() {
			const type = this.viewType === 'month' ? 'week' : 'month';
			this.viewType = type;
			this.virtually_data = [];
			if (type === 'week') {
				this.initWeekCalender(this.current, 'current');
			} else {
				this.initMonthCalender(this.current);
			}
		},

		// 初始化月份日历
		initMonthCalender(value) {
			let date = new Date();
			if (value) {
				date = new Date(value);
			}
			const nowMonth = date.getMonth() + 1;
			const nowYear = date.getFullYear();
			const nowDay = date.getDate();

			this._nowYear = nowYear;
			this._nowMonth = nowMonth;
			this._nowDay = nowDay;

			const days = getMonthDays(nowMonth, nowYear);
			const start_date = new Date(nowYear, nowMonth - 1, 1);
			const end_date = new Date(nowYear, nowMonth - 1, days);
			const prev_date = new Date(start_date.getTime() - 1);
			const prev_date_days = prev_date.getDate();
			const next_date = new Date(end_date.getTime() + 86401 * 1000);
			const start_week = start_date.getDay();
			const date_arrs = [];

			if (this.minDate) {
				const cd = new Date(date);
				const firstDay = formattedDate(new Date(cd.getFullYear(), cd.getMonth(), 1));
				this.prevDisabled = getDaysDifference(firstDay, this.minDate) <= 0;
			}

			// 上月
			for (let i = prev_date_days - start_week + 1; i <= prev_date_days; i++) {
				const _d = this.dateComplement(`${prev_date.getFullYear()}-${prev_date.getMonth() + 1}-${i}`);
				const dayDiff = getDaysDifference(today, _d);
				const minDayDiff = this.minDate ? getDaysDifference(_d, this.minDate) : 0;
				date_arrs.push({
					day: i,
					// day: today == _d ? '今' : i,
					today: today == _d,
					type: 'prev',
					date: _d,
					after: dayDiff < 0,
					before: minDayDiff < 0
				});
			}

			// 当前月
			for (let i = 1; i <= days; i++) {
				const _d = this.dateComplement(`${nowYear}-${nowMonth}-${i}`);
				const dayDiff = getDaysDifference(today, _d);
				const minDayDiff = this.minDate ? getDaysDifference(_d, this.minDate) : 0;
				date_arrs.push({
					day: i,
					// day: today == _d ? '今' : i,
					today: today == _d,
					type: 'current',
					date: _d,
					after: dayDiff < 0,
					before: minDayDiff < 0
				});
			}

			// 下月
			const date_arrs_length = date_arrs.length;
			for (let i = 1; i <= 42 - date_arrs_length; i++) {
				const _d = this.dateComplement(`${next_date.getFullYear()}-${next_date.getMonth() + 1}-${i}`);
				const dayDiff = getDaysDifference(today, _d);
				const minDayDiff = this.minDate ? getDaysDifference(_d, this.minDate) : 0;
				date_arrs.push({
					day: i,
					// day: today == _d ? '今' : i,
					today: today == _d,
					type: 'next',
					date: _d,
					after: dayDiff < 0,
					before: minDayDiff < 0
				});
			}

			this.days = date_arrs;
			this.__days = date_arrs;
			if (!this.virtually_data.length) {
				this.virtually_data = [...this.__days];
			}
			this.setCalenderDots();
		},

		// 初始化星期日历
		initWeekCalender(date, type = 'current') {
			const typeMap = {
				prev: -7,
				current: 0,
				next: 7
			};
			const timestamp = 3600 * 24 * 1000;
			const t_date = new Date(new Date(date || this.current).getTime() + typeMap[type] * timestamp);
			const ty = t_date.getFullYear();
			const tm = t_date.getMonth();
			const td = t_date.getDate() - t_date.getDay();
			const weekStart = new Date(ty, tm, td);
			const weekDays = [];
			for (var i = 0; i < 7; i++) {
				const _d = getAppointDate(formattedDate(weekStart), i);
				const day = new Date(_d).getDate();
				const dayDiff = getDaysDifference(today, _d);
				const minDayDiff = this.minDate ? getDaysDifference(_d, this.minDate) : 0;
				weekDays.push({
					day: day,
					// day: today == _d ? '今' : day,
					today: today == _d,
					type: 'current',
					date: _d,
					after: dayDiff < 0,
					before: minDayDiff < 0
				});
			}
			const selectDate = weekDays[new Date(this.current).getDay()].date;
			let _default = selectDate;
			if (!this.allowFuture) {
				const weekDiff = getDaysDifference(today, weekDays[weekDays.length - 1].date);
				this.nextDisabled = weekDiff <= 0;
				if (getDaysDifference(today, _default) < 0) {
					_default = today;
				}
			}
			if (this.minDate) {
				const firstDay = weekDays[0].date;
				const minDiff = getDaysDifference(firstDay, this.minDate);
				this.prevDisabled = minDiff <= 0;
				if (getDaysDifference(selectDate, this.minDate) <= 0) {
					_default = this.minDate;
				}
			}
			this.current = _default;
			this.days = weekDays;
			this.__days = weekDays;
			if (!this.virtually_data.length) {
				this.virtually_data = [...this.__days];
			}
			this.setCalenderDots();
		},

		// 设置日历数据点
		setCalenderDots() {
			if (this.dots.length) {
				for (var d = 0; d < this.dots.length; d++) {
					for (var i = 0; i < this.days.length; i++) {
						if (this.dots[d].date == this.days[i].date) {
							this.$set(this.days, i, { ...this.days[i], ...this.dots[d] });
						}
					}
				}
			}
			this.$nextTick(() => {
				this.$emit('inited', this.days);
			});
		},

		// 月份切换计算
		getMonth(type) {
			let nowYear = parseInt(this._nowYear);
			let nowMonth = parseInt(this._nowMonth);
			let nowDay = new Date(this.current).getDate();

			if (type == 'prev') {
				if (nowMonth == 1) {
					nowMonth = 12;
					nowYear = nowYear - 1;
				} else {
					nowMonth--;
				}
			} else if (type == 'next') {
				if (nowMonth == 12) {
					nowMonth = 1;
					nowYear = nowYear + 1;
				} else {
					nowMonth++;
				}
			}

			let days = getMonthDays(nowMonth, nowYear);
			if (nowDay > days) {
				nowDay = days;
			}
			return this.dateComplement(`${nowYear}-${nowMonth}-${nowDay}`);
		},

		// 日期格式化
		dateComplement(date) {
			return date.replace(/-(\d)(?!\d)/g, '-0$1');
		},

		// 手势滑动操作
		touchstart(e) {
			if (this.changeType) return;
			this.__st = e.timeStamp;
			startX = e.touches[0].pageX;
		},

		touchmove(e) {
			if (this.changeType) return;
			endX = e.touches[0].pageX;
		},

		touchend(e) {
			if (this.changeType) return;
			try {
				// 300毫秒快速操作，切换日历
				if (e.timeStamp - this.__st > 300 || endX == 0) {
					startX = 0;
					endX = 0;
					return;
				}

				const rangeX = Math.abs(endX - startX);
				// 滑动范围限制
				if (rangeX < 80) return;
				let type = '';

				if (endX < startX) {
					if (this.nextDisabled) return;
					type = 'next';
				} else if (endX > startX) {
					if (this.prevDisabled) return;
					type = 'prev';
				}

				startX = 0;
				endX = 0;

				this.handleChange(type);

				// 500ms节流
				setTimeout(() => {
					this.changeType = '';
					this.virtually_data = [...this.__days];
				}, 500);
			} catch (e) {
				console.error('touchend', e);
			}
		}
	}
};
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>
