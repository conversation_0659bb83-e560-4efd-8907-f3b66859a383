<template>
	<view class="terminalEdit-panel">
		<view class="page-content">
			<view class="meeting-info">
				<uv-textarea v-model="remarkValue" placeholder="请填写开发总结" count maxlength="500" adjustPosition border="none"></uv-textarea>
			</view>
			<view class="submit-button" @tap="submitTerminalEdit">提交</view>
		</view>
		<p-tabbar tabbarSelect="work"></p-tabbar>
	</view>
</template>

<script setup>
import { ref } from 'vue';
import {terminalrecordAdd, terminalrecordInfo, terminalrecordUpdate} from "@/common/api/terminal";
const remarkValue = ref('');
const id = ref(null);
const terminalId = ref(null);
import {
  onLoad
} from '@dcloudio/uni-app';

const handleRefresh = () => {
  const pages = getCurrentPages();
  const prePage = pages[pages.length - 2];
  prePage.$vm.refresh()
  uni.navigateBack();
}

const submitTerminalEdit = () => {
  if(!remarkValue.value) {
    uni.showToast({
      title: '请填写总结',
      icon: 'none'
    });
    return
  }
  if(id.value) {
    terminalrecordUpdate({
      id: id.value,
      name: '已进院',
      summarize: remarkValue.value,
      terminalId: terminalId.value
    }).then(res => {
      if(res.code === 0) {
        handleRefresh()
      }
    })
  } else {
    terminalrecordAdd({
      name: '已进院',
      summarize: remarkValue.value,
      terminalId: terminalId.value
    }).then(res => {
      if(res.code === 0) {
        handleRefresh()
      }
    })
  }

};

onLoad((option) => {
  terminalId.value = option.terminalId;
  id.value = option.id;
  if(id.value) {
    terminalrecordInfo({id: id.value}).then(res => {
      remarkValue.value = res.data.summarize
    })
  }
})
</script>

<style scoped lang="scss">
@import '@/static/scss/global.scss';
.terminalEdit-panel {
	.page-content {
		@include globalPageStyle();
		padding: 28rpx 20rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		.meeting-info {
			@include cardBgCommonStyle();
			box-sizing: border-box;
			width: 710rpx;
			height: 254rpx;
			:deep(.uv-textarea) {
				background-color: transparent !important;
				height: 100%;
				width: 100%;
				box-sizing: border-box;
				padding: 23rpx 25rpx;
				@include setlightFont(24rpx, 40rpx, #2f3133);
				.uv-textarea__field {
					height: 100% !important;
				}
				.textarea-placeholder {
					@include setlightFont(24rpx, 40rpx, #8d9094);
				}
				.uv-textarea__count {
					background-color: transparent !important;
				}
			}
		}
		.submit-button {
			@include commonSquareButtonStyle();
		}
	}
}
</style>
