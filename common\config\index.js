// 开发环境配置
const config = {
	baseUrl: 'https://test-gy-zsdl.guoyaoplat.com/basic-api',
	mapKey: 'McuIHjllOR7Ba6S9cB2bPsx1PT9aVFSY' //百度地图key
}
console.log(process.env.NODE_ENV, '环境');
if (process.env.NODE_ENV === 'development') {
	// config.baseUrl = 'http://192.168.1.24'; //邱
	config.baseUrl = 'https://test-gy-zsdl.guoyaoplat.com/basic-api'; //邱
	// config.baseUrl = 'https://test-gy-zsdl.guoyaoplat.com'; //测试环境
	// config.baseUrl = 'https://kszs.guoyaoplat.com/basic-api'; //正式环境
	// config.baseUrl = 'http://192.168.3.15:8089'; //邱
	// config.baseUrl = 'http://192.168.3.165:8080'; //霍
	// config.baseUrl = 'http://192.168.3.162:8080'; //黄
} else {
	config.baseUrl = 'https://test-gy-zsdl.guoyaoplat.com/basic-api';
}
export default config;

