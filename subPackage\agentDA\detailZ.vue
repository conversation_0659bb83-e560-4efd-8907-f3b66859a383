<template>
  <view class="detail-page">
    <!-- 基本信息 -->
    <view class="info-card">
      <view class="card-header">
        <view class="header-icon">
          <image src="./img/info.svg"></image>
        </view>
        <text class="header-title">基本信息</text>
      </view>
      <view class="card-content">
        <view class="info-row">
          <text class="info-label">代理商名称</text>
          <text class="info-value">{{ agentInfo.name }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">代理商编号</text>
          <text class="info-value">{{ agentInfo.code }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">代理商类型</text>
          <view class="info-value">
            <view class="type-tags">
              <van-tag v-if="agentInfo.type === '1'" type="primary"
                >企业</van-tag
              >
              <van-tag v-else type="error">个人</van-tag>
            </view>
          </view>
        </view>
        <view class="info-row">
          <text class="info-label">证件信息</text>
          <text class="info-value">{{ agentInfo.idTypeName }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">证件编号</text>
          <text class="info-value">{{ agentInfo.idNumber }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">合作状态</text>
          <view class="info-value">
            <van-tag type="success">正式</van-tag>
          </view>
        </view>
      </view>
    </view>

    <!-- 联系人信息 -->
    <view class="info-card">
      <view class="card-header">
        <view class="header-icon">
          <image src="./img/contact.svg"></image>
        </view>
        <text class="header-title">联系人信息</text>
      </view>
      <view class="card-content">
        <view class="info-row">
          <text class="info-label">联系人姓名</text>
          <text class="info-value">{{ agentInfo.contactPerson }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">身份证号</text>
          <text class="info-value">{{ agentInfo.personIdNumber }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">联系电话</text>
          <text class="info-value">{{ agentInfo.phone }}</text>
        </view>
      </view>
    </view>

    <!-- 地址信息 -->
    <view class="info-card">
      <view class="card-header">
        <view class="header-icon">
          <image src="./img/address.svg"></image>
        </view>
        <text class="header-title">地址信息</text>
      </view>
      <view class="card-content">
        <view class="info-row">
          <text class="info-label">所在地区</text>
          <text class="info-value"
            >{{ agentInfo.provinceName
            }}<text v-if="agentInfo.cityName"
              >/{{ agentInfo.cityName }}</text
            ></text
          >
        </view>
        <view class="info-row">
          <text class="info-label">详细地址</text>
          <text class="info-value">{{ agentInfo.address }}</text>
        </view>
      </view>
    </view>

    <!-- 业务覆盖 -->
    <view class="info-card">
      <view class="card-header">
        <view class="header-icon">
          <image src="./img/yewu.svg"></image>
        </view>
        <text class="header-title">业务覆盖</text>
      </view>
      <view class="card-content">
        <view class="info-row">
          <text class="info-label">覆盖省份/区域</text>
          <text class="info-value"
            >{{ agentInfo.primaryProvinceName
            }}<text>/{{ agentInfo.primaryCityName }}</text></text
          >
        </view>
        <view class="info-row">
          <text class="info-label">人员数量</text>
          <text class="info-value">{{ agentInfo.peopleNumberName }}</text>
        </view>
        <view class="info-row">
          <text class="info-label">财税能力</text>
          <text class="info-value">{{ agentInfo.fiscalCompetenceName }}</text>
        </view>
      </view>
    </view>

    <!-- 附件预览 -->
    <view class="info-card">
      <view class="card-header">
        <view class="header-icon">
          <image src="./img/file.svg"></image>
        </view>
        <text class="header-title">附件预览</text>
      </view>
      <view v-if="agentInfo.type === '1'" class="card-content">
        <view v-if="agentInfo.businessLicense" class="attachment-section">
          <text class="attachment-label">营业执照</text>
          <view class="attachment-preview">
            <image
              :src="agentInfo.businessLicense"
              class="license-image"
              mode="aspectFit"
              @click="previewImage(agentInfo.businessLicense)"
            ></image>
          </view>
        </view>
      </view>
      <view v-if="agentInfo.type === '1'" class="card-content">
        <view v-if="agentInfo.accountOpeningPermit" class="attachment-section">
          <text class="attachment-label">开户许可证</text>
          <view class="attachment-preview">
            <image
              :src="agentInfo.accountOpeningPermit"
              class="license-image"
              mode="aspectFit"
              @click="previewImage(agentInfo.accountOpeningPermit)"
            ></image>
          </view>
        </view>
      </view>
      <view v-if="agentInfo.type === '2'" class="card-content">
        <view v-if="agentInfo.idCardPositive" class="attachment-section">
          <text class="attachment-label">身份证正面(国徽)</text>
          <view class="attachment-preview">
            <image
              :src="agentInfo.idCardPositive"
              class="license-image"
              mode="aspectFit"
              @click="previewImage(agentInfo.idCardPositive)"
            ></image>
          </view>
        </view>
      </view>
      <view v-if="agentInfo.type === '2'" class="card-content">
        <view v-if="agentInfo.idCardNegative" class="attachment-section">
          <text class="attachment-label">身份证反面(头像)</text>
          <view class="attachment-preview">
            <image
              :src="agentInfo.idCardNegative"
              class="license-image"
              mode="aspectFit"
              @click="previewImage(agentInfo.idCardNegative)"
            ></image>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { getAgentDetail } from "@/common/api/agentDA/index.js";

const agentId = ref("");
const agentInfo = ref({});

onLoad((options) => {
  if (options.id) {
    agentId.value = options.id;
    loadAgentDetail();
  }
});

// 加载代理商详情
const loadAgentDetail = async () => {
  try {
    uni.showLoading({
      title: "加载中...",
    });

    const res = await getAgentDetail({ id: agentId.value });

    uni.hideLoading();

    if (res.code === 0) {
      agentInfo.value = res.data;
    } else {
      uni.showToast({
        title: res.msg,
        icon: "none",
      });
    }
  } catch (error) {
    uni.hideLoading();
    console.error("加载代理商详情失败:", error);
  }
};

// 预览图片
const previewImage = (url) => {
  const imageUrl = url;
  uni.previewImage({
    urls: [imageUrl],
    current: imageUrl,
  });
};
</script>

<style lang="scss" scoped>
@import "@/static/scss/global.scss";

.detail-page {
  min-height: 100vh;
  padding-bottom: env(safe-area-inset-bottom);
  margin: 20rpx 20rpx 0 20rpx;
}

.info-card {
  @include cardBgCommonStyle();
  margin-bottom: 24rpx;
  overflow: hidden;

  &:last-child {
    margin-bottom: 0;
  }
}

.card-header {
  display: flex;
  align-items: center;
  padding: 20rpx 0 20rpx 20rpx;

  .header-icon {
    margin-right: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32rpx;
    height: 32rpx;
  }

  .header-title {
    @include setBoldFont(28rpx, 40rpx, #2f3133);
    flex: 1;
  }
}

.card-content {
  padding: 0;
}

.info-row {
  display: flex;
  align-items: flex-start;
  padding-left: 20rpx;
  margin-bottom: 20rpx;

  &:last-child {
    border-bottom: none;
  }
}

.info-label {
  @include setlightFont(24rpx, 40rpx, #8d9094);
  width: 160rpx;
  flex-shrink: 0;
  margin-right: 10rpx;
}

.info-value {
  @include setlightFont(24rpx, 40rpx, #2f3133);
  flex: 1;
  word-break: break-all;

  .type-tags {
    display: inline-block;
  }
}

.attachment-section {
  padding: 0 20rpx 20rpx;
}

.attachment-label {
  @include setBoldFont(24rpx, 40rpx, #8d9094);
  display: block;
}

.attachment-preview {
  display: flex;
  justify-content: start;
  align-items: center;
}

.license-image {
  width: 100%;
  width: 160rpx;
  height: 160rpx;
  border-radius: 16rpx;
  cursor: pointer;
  transition: transform 0.2s ease;

  // &:hover {
  //   transform: scale(1.02);
  // }
}

// 标签样式覆盖
:deep(.van-tag) {
  border-radius: 8rpx !important;
  font-weight: 400 !important;
  font-size: 20rpx;
}

:deep(.van-tag--primary) {
  background: #e6effa !important;
  color: #0d6ce4 !important;
}

:deep(.van-tag--success) {
  background: #e8f7e8 !important;
  color: #19b21e !important;
}
:deep(.van-tag--error) {
  background: #faebeb !important;
  color: #fc474c !important;
}
</style>
