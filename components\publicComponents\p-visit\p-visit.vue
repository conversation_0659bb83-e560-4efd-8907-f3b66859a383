<template>
  <view class="tab-container">
    <view
      v-for="(tab, index) in tabs"
      :key="index"
      :class="['tab-item', { active: activeTab === index }]"
      @click="switchTab(index, tab.key)"
    >
      <template>
        <image
          class="icon"
          :src="
            activeTab !== index ? `${tab.icon}.png` : `${tab.icon}active.png`
          "
          mode=""
        ></image>
      </template>
      <view>{{ tab.label }}</view>
    </view>
  </view>
</template>

<script setup>
import { ref, watch } from "vue";
import { onShow } from "@dcloudio/uni-app";
import { useVisitStore } from "@/common/store/visit";
import { useUserStore } from "@/common/store/user";
import { getByCurrentTime } from "/common/api/sign/index.js";
import { formatTimestamp } from "/utils/utils.js";
const userStore = useUserStore();
const visitStore = useVisitStore();
const currentTime = ref("");
const tabs = [
  { icon: "/static/image/icon/daka", label: "拜访打卡", key: "visit" },
  { icon: "/static/image/icon/record", label: "打卡记录", key: "record" },
];
const activeTab = ref(visitStore.$state.activeTab);
// 用户信息
const salesmanId = ref(""); // 业务员 ID，确保不存在时为 null
const userInfo = userStore.getUserInfo;
salesmanId.value = userInfo && userInfo.id ? userInfo.id : null; // 业务员
const visitTime = (type) => {
  console.log("================进来了");

  visitStore.setVisitType(type);
  // 1 终端拜访,2市场协访,3代理商拜访
  let parmas = {
    createDate: currentTime.value,
    personId: salesmanId.value,
    visitType: type,
  };
  getByCurrentTime(parmas).then((res) => {
    const { code, data } = res;
    if (code == 0 && data) {
      visitStore.setSiginType(2);
      visitStore.setNeedRefresh(true);
      visitStore.setIsIndex(true);
      visitStore.setClientShop({
        id: data.purMerchantId,
        name: data.purMerchantName,
        address: data.destination,
        latitude: data.purMerchantLatitude,
        longitude: data.purMerchantLongitude,
        userId: data.coachedPersonId,
        userName: data.coachedPersonName,
        agentId: data.agentEmployeeId,
        agentName: data.agentEmployeeName,
        createDate: data.createDate,
        // locationList: data.crmLocationList,
        locationList: null,
      });
      visitStore.setClientDept({
        departmentId: data.baseHospitalId,
        departmentName: data.baseHospitalName,
      });
      visitStore.setClientDoctor({
        ...visitStore.$state.clientDoctor,
        name: data.doctorName,
        id: data.doctorId,
        init: true,
      });
      visitStore.setAgent({
        agentEmployeeId: data.agentEmployeeId,
        agentEmployeeName: data.agentEmployeeName,
        agentId: data.agentId,
        agentName: data.agentName,
        isAgent: data.agentIsPresent,
      });
      visitStore.setAgentEmployee({
        personName: data.coachedPersonName,
        departNames: data.departNames,
        isScene: data.crmIsPresent,
        personId: data.coachedPersonId,
        id: data.id,
        isEvaluate: data?.isEvaluate,
        personCode: data.coachedErpCode,
      });
      uni.reLaunch({
        url: `/pages/subVisit/createVisit/createVisit`,
      });
    } else {
      if (type == "1") {
        uni.reLaunch({
          url: `/pages/subVisit/selectClientShop/selectClientShop`,
        });
      }
      if (type == "3") {
        uni.reLaunch({
          url: `/pages/subVisit/selectAgent/selectAgent`,
        });
      }
      if (type == "2") {
        uni.reLaunch({
          url: `/pages/subVisit/selectSub/selectSub`,
        });
      }
    }
  });
};
const switchTab = (index, key) => {
  visitStore.setActiveTab(index);
  switch (key) {
    case "record":
      uni.reLaunch({
        url: "/pages/subVisit/signRecord/signRecord",
      });
      break;
    case "visit":
      visitTime(visitStore.$state.visitType);
      break;
  }
};
onShow(() => {
  const timestamp = Date.now();
  currentTime.value = formatTimestamp(timestamp);
});
watch(
  () => visitStore.$state.activeTab,
  (newVal) => {
    activeTab.value = newVal;
  }
);
</script>

<style scoped lang="scss">
.icon {
  width: 32rpx;
  height: 32rpx;
  margin-top: 8rpx;
}
.tab-container {
  position: fixed;
  width: 95%;
  bottom: 180rpx;
  height: 82rpx;
  display: flex;
  justify-content: space-around;
  margin-left: 20rpx;
  background-color: rgba(255, 255, 255, 1);
}

.tab-item {
  text-align: center;
  width: 50%;
  height: 100%;
  color: var(--, #4068f5);
  font-feature-settings: "liga" off, "clig" off;
  font-family: OPPOSans;
  font-size: 24rpx;
  font-style: normal;
  font-weight: 400;
  border: 2rpx solid #4068f5;
  transition: all 0.3s ease;
  &:nth-child(1) {
    border-radius: 16rpx 0 0 16rpx;
  }
  &:nth-child(2) {
    border-radius: 0 16rpx 16rpx 0;
  }
}

.tab-item.active {
  color: #fff;
  background: linear-gradient(180deg, #52acff -12%, #263af0 117%);
  filter: drop-shadow(0px 2px 8px rgba(0, 0, 0, 0.12));
}
</style>
